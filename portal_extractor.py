import requests
import re
import threading
from PyQt5.QtCore import QObject, pyqtSignal

class PortalExtractor(QObject):
    """Class for handling IPTV portal extraction functionality"""

    # Signals for updating UI
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # current, total
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.threads = []
        self.max_threads = 10
        self.timeout = 5
        self.results = []
        self.total_count = 0
        self.processed_count = 0
        self.filter_keywords = ["iptv", "mac", "xtream", "/c/", "stalker", "portal", "m3u", "stb", "ministra"]
        self.filter_enabled = True
        self.proxies = None
        self.use_advanced_search = True

        # Advanced search queries for finding IPTV portals
        self.advanced_search_queries = [
            "inurl:/c/ \"Your STB\"",
            "inurl:/c/ \"stalker_portal\"",
            "inurl:/c/ \"MAC address\"",
            "inurl:/stalker_portal/ \"login\"",
            "inurl:/webplayer/login.php",
            "intitle:\"IPTV Portal\"",
            "intitle:\"Stalker Portal\"",
            "intitle:\"Ministra Portal\"",
            "inurl:/c/ filetype:txt",
            "inurl:/stalker_apps/",
            "inurl:/stalker_launcher_apps/",
            "intext:\"MAC address\" inurl:/c/",
            "inurl:portal intext:\"username\" intext:\"password\"",
            "inurl:xtream intext:\"username\" intext:\"password\""
        ]

    def extract_from_file(self, file_path):
        """Extract portals from a file with URL:login:pass format"""
        portals = []
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # Extract all URLs from the line
                        urls = self.extract_urls_from_line(line)

                        for url in urls:
                            # Clean up URL
                            url = self.clean_portal_url(url)
                            if url and url not in portals and self.is_iptv_related(url):
                                portals.append(url)
                                self.result_signal.emit({"url": url, "source": "file"})

            return portals
        except Exception as e:
            print(f"Error extracting portals from file: {e}")
            return []

    def extract_from_directory(self, directory_path):
        """Extract portals from all text files in a directory"""
        portals = []
        try:
            import os
            # Get all text files in directory
            text_files = [f for f in os.listdir(directory_path) if f.endswith('.txt')]

            if not text_files:
                self.status_signal.emit("No text files found in directory")
                return []

            self.status_signal.emit(f"Found {len(text_files)} text files. Processing...")

            # Maximum file size to process fully (10MB)
            MAX_FILE_SIZE = 10 * 1024 * 1024

            # Process each file
            for file_index, file_name in enumerate(text_files):
                file_path = os.path.join(directory_path, file_name)
                try:
                    # Update progress
                    progress_percent = int((file_index / len(text_files)) * 100)
                    self.status_signal.emit(f"Processing file {file_index+1}/{len(text_files)} ({progress_percent}%): {file_name}")

                    # Check file size
                    file_size = os.path.getsize(file_path)

                    # For large files, use a different approach
                    if file_size > MAX_FILE_SIZE:
                        self.status_signal.emit(f"Large file detected ({file_size/1024/1024:.2f} MB). Processing in chunks...")

                        # Process large file in chunks
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            # Process the file in chunks of 1MB
                            chunk_size = 1024 * 1024  # 1MB chunks
                            chunk_count = 0

                            while True:
                                chunk = f.read(chunk_size)
                                if not chunk:
                                    break

                                chunk_count += 1
                                if chunk_count % 5 == 0:  # Update status every 5 chunks
                                    self.status_signal.emit(f"Processing chunk {chunk_count} of file {file_name}...")

                                # Extract URLs from chunk - use simpler regex for speed
                                urls = re.findall(r'https?://[^\s"\'<>]+', chunk)

                                # Process URLs in batches
                                for i in range(0, len(urls), 50):
                                    batch = urls[i:i+50]
                                    for url in batch:
                                        # Basic filtering for speed
                                        if any(keyword in url.lower() for keyword in ["iptv", "xtream", "stalker", "/c/", "portal"]):
                                            # Clean up URL
                                            url = self.clean_portal_url(url)
                                            if url and url not in portals:
                                                portals.append(url)
                                                self.result_signal.emit({"url": url, "source": f"file:{file_name}"})
                    else:
                        # For smaller files, process normally
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()

                            # Extract all URLs from the content
                            urls = re.findall(r'https?://[^\s"\'<>]+', content)

                            # Look for portal-specific patterns - simplified for performance
                            portal_patterns = [
                                r'portal\s*:\s*(https?://[^\s"\'<>]+)',
                                r'url\s*:\s*(https?://[^\s"\'<>]+)'
                            ]

                            for pattern in portal_patterns:
                                portal_matches = re.findall(pattern, content, re.IGNORECASE)
                                urls.extend(portal_matches)

                            # Process URLs in batches to avoid UI freezing
                            batch_size = 50
                            for i in range(0, len(urls), batch_size):
                                batch = urls[i:i+batch_size]
                                for url in batch:
                                    # Clean up URL
                                    url = self.clean_portal_url(url)
                                    if url and url not in portals and self.is_iptv_related(url):
                                        portals.append(url)
                                        self.result_signal.emit({"url": url, "source": f"file:{file_name}"})

                                # Allow UI to update
                                import time
                                time.sleep(0.01)

                except Exception as e:
                    self.status_signal.emit(f"Error processing file {file_name}: {str(e)}")
                    continue

            self.status_signal.emit(f"Extracted {len(portals)} potential portals from directory")
            return portals
        except Exception as e:
            self.status_signal.emit(f"Error extracting portals from directory: {str(e)}")
            return []

    def extract_urls_from_line(self, line):
        """Extract URLs from a line of text"""
        urls = []

        # Try to extract URLs using regex
        regex_urls = re.findall(r'https?://[^\s"\'<>]+', line)
        urls.extend(regex_urls)

        # Try to extract URL from URL:login:pass format
        parts = line.split(':')
        if len(parts) >= 2:
            # Handle http:// or https:// in URL
            if parts[0] == 'http' or parts[0] == 'https':
                url = f"{parts[0]}:{parts[1]}"
                # Add port if exists
                if len(parts) > 3 and parts[2].isdigit():
                    url += f":{parts[2]}"
                urls.append(url)
            elif '.' in parts[0]:  # Likely a domain name
                url = parts[0]
                urls.append(url)

        return urls

    def clean_portal_url(self, url):
        """Clean and format portal URL"""
        try:
            # Add http:// if missing
            if not url.startswith('http://') and not url.startswith('https://'):
                url = f"http://{url}"

            # Remove query parameters except for specific IPTV-related ones
            if '?' in url:
                base_url, params = url.split('?', 1)
                if not any(param in params.lower() for param in ['username', 'password', 'mac', 'portal', 'xtream']):
                    url = base_url

            # Handle special cases for IPTV portals
            if '/c/' in url:
                # Keep /c/ path for Xtream Codes
                url = re.sub(r'(/c/[^/]*)?(/.*)?$', '/c/', url)
            elif any(path in url for path in ['/player_api.php', '/xmltv.php', '/panel_api.php']):
                # Keep these specific endpoints
                for endpoint in ['/player_api.php', '/xmltv.php', '/panel_api.php']:
                    if endpoint in url:
                        url = url.split(endpoint)[0] + endpoint
                        break
            elif '/stalker_portal' in url:
                # Keep stalker_portal path
                url = url.split('/stalker_portal')[0] + '/stalker_portal'
            elif '/portal' in url and not url.endswith('/portal'):
                # Keep portal path
                url = url.split('/portal')[0] + '/portal'
            else:
                # Remove trailing paths for other URLs
                url = re.sub(r'(/[^/]*)?$', '', url)

            # Remove trailing slash
            url = url.rstrip('/')

            return url
        except Exception as e:
            print(f"Error cleaning URL {url}: {e}")
            return url

    def is_iptv_related(self, url):
        """Check if URL is related to IPTV based on keywords"""
        if not self.filter_enabled:
            return True

        url_lower = url.lower()

        # Always include URLs with these specific patterns
        if any(pattern in url_lower for pattern in ['/c/', 'xtream', 'stalker', 'portal', 'player_api.php', 'panel_api.php']):
            return True

        # Check against filter keywords
        return any(keyword.lower() in url_lower for keyword in self.filter_keywords)

    def search_portals_online(self):
        """Search for latest portal lists online"""
        self.status_signal.emit("Searching for portals online...")
        portals = []

        # List of sources to check for portal lists - reduced for performance
        sources = [
            "https://raw.githubusercontent.com/iptv-org/iptv/master/sources.json",
            "https://raw.githubusercontent.com/Free-TV/IPTV/master/playlist.m3u8"
        ]

        # Common request headers
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive"
        }

        # Process each source safely
        for source_index, source in enumerate(sources):
            try:
                self.status_signal.emit(f"Checking source {source_index+1}/{len(sources)}: {source}")

                # Use a shorter timeout to avoid hanging
                try:
                    response = requests.get(
                        source,
                        timeout=10,
                        verify=False,
                        headers=headers,
                        proxies=self.proxies
                    )

                    if response.status_code == 200:
                        content = response.text

                        # Extract URLs with a more robust pattern
                        urls = re.findall(r'https?://[^\s"\'<>]+', content)

                        # Limit the number of URLs to process
                        if len(urls) > 500:
                            self.status_signal.emit(f"Found {len(urls)} URLs, limiting to 500 for performance")
                            urls = urls[:500]

                        # Process URLs in batches to avoid UI freezing
                        batch_size = 20
                        for i in range(0, len(urls), batch_size):
                            batch = urls[i:i+batch_size]
                            for url in batch:
                                try:
                                    # Quick pre-filter for performance
                                    if not any(keyword in url.lower() for keyword in ["iptv", "xtream", "stalker", "/c/", "portal"]):
                                        continue

                                    # Clean up URL
                                    clean_url = self.clean_portal_url(url)
                                    if clean_url and clean_url not in portals and self.is_iptv_related(clean_url):
                                        portals.append(clean_url)
                                        self.result_signal.emit({"url": clean_url, "source": "online"})
                                except Exception:
                                    # Skip problematic URLs
                                    continue

                            # Update status periodically
                            self.status_signal.emit(f"Found {len(portals)} potential portals so far...")

                            # Allow UI to update
                            import time
                            time.sleep(0.01)

                except requests.exceptions.RequestException as req_error:
                    self.status_signal.emit(f"Could not access {source}: {str(req_error)}")
                    continue

            except Exception as e:
                self.status_signal.emit(f"Error processing source {source}: {str(e)}")
                continue

        # Use advanced search if enabled - but limit the number of queries
        if self.use_advanced_search:
            self.status_signal.emit("Performing advanced search for IPTV portals...")

            # Limit to just a few most effective queries
            limited_queries = self.advanced_search_queries[:5]

            try:
                # Use advanced search queries
                for query_index, query in enumerate(limited_queries):
                    try:
                        self.status_signal.emit(f"Searching query {query_index+1}/{len(limited_queries)}: {query}")

                        # Use DuckDuckGo instead of Google to avoid blocking
                        search_url = f"https://duckduckgo.com/html/?q={query.replace(' ', '+')}"

                        response = requests.get(
                            search_url,
                            headers=headers,
                            timeout=10,
                            verify=False,
                            proxies=self.proxies
                        )

                        if response.status_code == 200:
                            content = response.text

                            # Extract URLs
                            urls = re.findall(r'https?://[^\s"\'<>]+', content)

                            # Limit the number of URLs to process
                            if len(urls) > 100:
                                urls = urls[:100]

                            # Process in batches
                            batch_size = 20
                            for i in range(0, len(urls), batch_size):
                                batch = urls[i:i+batch_size]
                                for url in batch:
                                    try:
                                        # Quick pre-filter
                                        if not any(keyword in url.lower() for keyword in ["iptv", "xtream", "stalker", "/c/", "portal"]):
                                            continue

                                        # Clean up URL
                                        clean_url = self.clean_portal_url(url)
                                        if clean_url and clean_url not in portals and self.is_iptv_related(clean_url):
                                            portals.append(clean_url)
                                            self.result_signal.emit({"url": clean_url, "source": f"search:{query}"})
                                    except Exception:
                                        continue

                                # Allow UI to update
                                import time
                                time.sleep(0.01)

                    except Exception as query_error:
                        self.status_signal.emit(f"Error with query '{query}': {str(query_error)}")
                        continue
            except Exception as search_error:
                self.status_signal.emit(f"Error during advanced search: {str(search_error)}")

        # Add known IPTV domains
        try:
            known_domains = [
                "http://iptv.com",
                "http://xtream-codes.com",
                "http://stalkerportal.com",
                "http://iptvproviders.net",
                "http://iptvsource.com"
            ]

            for domain in known_domains:
                if domain not in portals:
                    portals.append(domain)
                    self.result_signal.emit({"url": domain, "source": "known"})
        except Exception as e:
            self.status_signal.emit(f"Error adding known domains: {str(e)}")

        self.status_signal.emit(f"Search completed. Found {len(portals)} potential portals.")
        return portals

    def verify_portal(self, url):
        """Verify if URL is a valid IPTV portal"""
        try:
            # Check only the most common portal endpoints for performance
            endpoints = [
                "",  # Base URL
                "/c/",
                "/player_api.php",
                "/xui/login"
            ]

            # Add headers to mimic a browser
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive"
            }

            # Use a very short timeout to avoid hanging
            short_timeout = min(3, self.timeout)

            for endpoint in endpoints:
                check_url = url + endpoint if not url.endswith(endpoint) else url

                try:
                    # Use a shorter timeout to avoid hanging
                    response = requests.get(
                        check_url,
                        headers=headers,
                        timeout=short_timeout,
                        verify=False,
                        allow_redirects=True,
                        proxies=self.proxies
                    )

                    # Check if response indicates a portal
                    if response.status_code == 200:
                        # Check content-type first
                        content_type = response.headers.get('Content-Type', '').lower()
                        if 'text/html' not in content_type and 'application/json' not in content_type:
                            continue

                        # Get a sample of the content instead of the full response
                        # This is much faster for large responses
                        content_sample = response.text[:5000].lower()

                        # Check for the most common portal indicators
                        portal_indicators = [
                            "xtream", "stalker", "iptv", "player_api", "portal",
                            "mag", "stb", "username", "password", "login"
                        ]

                        if any(indicator in content_sample for indicator in portal_indicators):
                            return True

                    # Some portals might return 401 or 403 for authentication required
                    elif response.status_code in [401, 403]:
                        # Check headers for portal indicators
                        headers_str = str(response.headers).lower()
                        if any(indicator in headers_str for indicator in ["xtream", "stalker", "iptv", "www-authenticate"]):
                            return True
                except requests.exceptions.Timeout:
                    # Skip timeouts and continue with next endpoint
                    continue
                except requests.exceptions.RequestException:
                    # Skip connection errors for this endpoint
                    continue
                except Exception:
                    # Skip any other errors for this endpoint
                    continue

            return False
        except Exception as e:
            print(f"Error verifying portal {url}: {e}")
            return False

    def worker(self, urls):
        """Worker thread for verifying multiple URLs"""
        for url in urls:
            if not self.running:
                break

            # Skip URLs that don't look like IPTV portals for performance
            if not self.is_likely_portal(url):
                # Still update progress
                self.processed_count += 1
                self.progress_signal.emit(self.processed_count, self.total_count)
                continue

            # Verify the URL
            is_portal = self.verify_portal(url)

            if is_portal:
                result = {
                    "url": url,
                    "is_portal": True
                }
                self.result_signal.emit(result)

            # Update progress
            self.processed_count += 1
            self.progress_signal.emit(self.processed_count, self.total_count)

        # Thread completed
        return

    def is_likely_portal(self, url):
        """Quick check if a URL is likely to be an IPTV portal"""
        url_lower = url.lower()

        # Check for common portal indicators in the URL
        portal_indicators = [
            "iptv", "xtream", "stalker", "/c/", "portal", "player_api",
            "panel_api", "xui", "stb", "mag"
        ]

        return any(indicator in url_lower for indicator in portal_indicators)

    def start_verification(self, urls):
        """Start verifying URLs as portals"""
        if self.running:
            return False

        self.running = True
        self.processed_count = 0

        # Limit the number of URLs to verify for performance
        MAX_URLS = 1000
        if len(urls) > MAX_URLS:
            self.status_signal.emit(f"Limiting verification to {MAX_URLS} URLs for performance")
            urls = urls[:MAX_URLS]

        self.total_count = len(urls)
        self.results = []

        # Clear previous threads
        self.threads = []

        # Use fewer threads for better performance
        actual_threads = min(self.max_threads, 5)

        # Split work across threads
        chunk_size = max(1, len(urls) // actual_threads)
        for i in range(0, len(urls), chunk_size):
            chunk = urls[i:i+chunk_size]
            thread = threading.Thread(
                target=self.worker,
                args=(chunk,)
            )
            thread.daemon = True
            self.threads.append(thread)
            thread.start()

        return True

    def stop_verification(self):
        """Stop the verification process"""
        self.running = False

        # Wait for all threads to complete
        for thread in self.threads:
            if thread.is_alive():
                thread.join(1.0)  # Wait with timeout

        self.status_signal.emit("Verification stopped")
        return True
