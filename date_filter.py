"""
This module implements the date filtering functionality for IPTV scanner results.
It ensures that expired subscriptions from 2024 or earlier are not sent to Telegram.
"""

from datetime import datetime

def should_send_to_telegram(expires_date_str):
    """
    Determine if a result should be sent to Telegram based on expiration date.
    
    Args:
        expires_date_str (str): Expiration date string in various possible formats
        
    Returns:
        bool: True if the result should be sent to Telegram, False otherwise
    """
    # If no expiration date or invalid format, default to sending
    if not expires_date_str or expires_date_str.lower() in ['unknown', 'n/a', 'never', 'unlimited']:
        return True
    
    try:
        # Try different date formats
        date_formats = [
            '%Y-%m-%d',           # 2025-05-16
            '%d-%m-%Y',           # 16-05-2025
            '%d/%m/%Y',           # 16/05/2025
            '%m/%d/%Y',           # 05/16/2025
            '%Y/%m/%d',           # 2025/05/16
            '%d.%m.%Y',           # 16.05.2025
            '%Y.%m.%d',           # 2025.05.16
            '%d %b %Y',           # 16 May 2025
            '%d %B %Y',           # 16 May 2025
            '%b %d, %Y',          # May 16, 2025
            '%B %d, %Y',          # May 16, 2025
            '%Y-%m-%d %H:%M:%S',  # 2025-05-16 10:30:00
            '%d-%m-%Y %H:%M:%S',  # 16-05-2025 10:30:00
            '%d/%m/%Y %H:%M:%S',  # 16/05/2025 10:30:00
            '%m/%d/%Y %H:%M:%S',  # 05/16/2025 10:30:00
        ]
        
        # Try each format until one works
        expiration_date = None
        for date_format in date_formats:
            try:
                expiration_date = datetime.strptime(expires_date_str, date_format)
                break
            except ValueError:
                continue
        
        # If no format worked, try to extract year
        if expiration_date is None:
            # Try to extract year from the string
            import re
            year_match = re.search(r'20\d{2}', expires_date_str)
            if year_match:
                year = int(year_match.group(0))
                # If year is 2025 or later, consider it valid
                return year >= 2025
            else:
                # If no year found, default to sending
                return True
        
        # Check if expiration date is 2025 or later
        return expiration_date.year >= 2025
    
    except Exception:
        # If any error occurs during parsing, default to sending
        return True
