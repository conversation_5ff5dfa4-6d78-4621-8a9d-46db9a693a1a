"""
This module implements the portal extraction functionality for IPTV tools.
It supports extracting portal URLs from various sources and formats.
"""

import re
import requests
from bs4 import BeautifulSoup
import logging

logger = logging.getLogger("iptv_tools.portal_extractor")

class PortalExtractor:
    """Class for extracting IPTV portal URLs from various sources"""
    
    def __init__(self):
        self.portals = []
        self.sources = [
            "https://iptv-org.github.io/iptv/",
            "https://github.com/iptv-org/iptv",
            "https://www.iptvcat.com/",
            "https://www.iptvsource.com/free-iptv-links/",
            "https://github.com/Free-IPTV/Countries"
        ]
    
    def extract_from_file(self, file_path):
        """
        Extract portal URLs from a text file.
        
        Args:
            file_path (str): Path to the file containing URLs
            
        Returns:
            list: List of extracted portal URLs
        """
        extracted_portals = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Handle URL:login:pass format
                if ':' in line:
                    parts = line.split(':')
                    if len(parts) >= 3:
                        # Extract URL part
                        url_part = ':'.join(parts[:2])
                        if self._is_valid_portal(url_part):
                            extracted_portals.append(url_part)
                    else:
                        # Check if it's a valid portal URL
                        if self._is_valid_portal(line):
                            extracted_portals.append(line)
                else:
                    # Check if it's a valid portal URL
                    if self._is_valid_portal(line):
                        extracted_portals.append(line)
            
            # Update the portals list
            self.portals.extend(extracted_portals)
            return extracted_portals
            
        except Exception as e:
            logger.error(f"Error extracting portals from file: {str(e)}")
            return []
    
    def search_online(self, max_results=100):
        """
        Search for IPTV portals online from known sources.
        
        Args:
            max_results (int): Maximum number of results to return
            
        Returns:
            list: List of found portal URLs
        """
        found_portals = []
        
        try:
            # Create a session for better performance
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            
            # Search each source
            for source in self.sources:
                try:
                    response = session.get(source, timeout=10)
                    if response.status_code == 200:
                        # Parse the HTML content
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        # Extract all links
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            
                            # Check if it's a valid portal URL
                            if self._is_valid_portal(href):
                                found_portals.append(href)
                            
                            # Check if we've reached the maximum number of results
                            if len(found_portals) >= max_results:
                                break
                    
                    # Check if we've reached the maximum number of results
                    if len(found_portals) >= max_results:
                        break
                        
                except Exception as e:
                    logger.error(f"Error searching source {source}: {str(e)}")
                    continue
            
            # Update the portals list
            self.portals.extend(found_portals)
            return found_portals
            
        except Exception as e:
            logger.error(f"Error searching for portals online: {str(e)}")
            return []
    
    def _is_valid_portal(self, url):
        """
        Check if a URL is a valid IPTV portal.
        
        Args:
            url (str): URL to check
            
        Returns:
            bool: True if the URL is a valid portal, False otherwise
        """
        # Check if it's a URL
        if not url.startswith(('http://', 'https://')):
            return False
        
        # Check for common portal patterns
        portal_patterns = [
            # Standard portal with port
            r'https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(:[0-9]+)?',
            # Portal with /c/ path
            r'https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/c/',
            # Portal with specific paths
            r'https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/(iptv|portal|player|live|api)',
            # Portal with specific ports
            r'https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}:(80|8080|25443|8800|8888)'
        ]
        
        for pattern in portal_patterns:
            if re.match(pattern, url):
                return True
        
        return False
    
    def get_portals(self):
        """
        Get the list of extracted portals.
        
        Returns:
            list: List of portal URLs
        """
        return self.portals
    
    def clear_portals(self):
        """Clear the list of portals."""
        self.portals = []
