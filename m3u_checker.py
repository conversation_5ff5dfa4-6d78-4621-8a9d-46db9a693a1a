import re
import threading
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
import logging
from utils import RequestsSession, format_proxy, save_error_to_file, send_to_telegram
from stats import StatsTracker
from config import get_config

# Get logger
logger = logging.getLogger("iptv_checker.m3u")

class M3UChecker(QObject):
    """Class for checking M3U playlist URLs"""

    # Define signals
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False

        # Load configuration from config
        self.max_threads = get_config('max_threads', 10)
        self.timeout = get_config('timeout', 10)
        self.auto_save = get_config('auto_save', True)
        self.telegram_enabled = get_config('telegram_enabled', False)
        self.telegram_bot_token = get_config('telegram_bot_token', "")
        self.telegram_chat_id = get_config('telegram_chat_id', "")

        # Initialize locks and counters
        self.proxies = []
        self.current_proxy_index = 0
        self.proxy_lock = threading.Lock()
        self.results_lock = threading.Lock()
        self.progress_lock = threading.Lock()
        self.total_checked = 0
        self.total_to_check = 0
        self.good_results = []

        # Initialize stats tracker
        self.stats = StatsTracker('m3u')

        # Try to install PySocks if not already installed
        try:
            import pip
            try:
                import socks
            except ImportError:
                pip.main(['install', 'PySocks'])
                logger.info("Installed PySocks package")
        except:
            logger.warning("Failed to install PySocks package")

    def load_proxies(self, file_path):
        """Load proxies from file"""
        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()

                self.proxies = []
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Parse proxy format
                    if line.startswith('socks5:'):
                        # Already formatted as socks5://user:pass@host:port
                        self.proxies.append(line)
                    elif '://' in line:
                        # Format: protocol://ip:port or protocol://user:pass@ip:port
                        self.proxies.append(line)
                    elif ':' in line:
                        parts = line.split(':')

                        # Handle SOCKS5 format: hostname:port:username:password
                        if len(parts) == 4:
                            # Format for SOCKS5 proxy with auth: socks5://username:password@hostname:port
                            self.proxies.append(f"socks5://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}")
                        # Handle standard format: ip:port or ip:port:user:pass
                        elif len(parts) == 2:
                            self.proxies.append(f"http://{parts[0]}:{parts[1]}")
                        elif len(parts) >= 4:
                            # Old format: ip:port:user:pass
                            self.proxies.append(f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}")

                return len(self.proxies)
        except Exception as e:
            print(f"Error loading proxies: {str(e)}")
            return 0

    def get_next_proxy(self):
        """Get next proxy from list in thread-safe way"""
        if not self.proxies:
            return None

        with self.proxy_lock:
            proxy = self.proxies[self.current_proxy_index]
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
            return proxy

    def start_check(self, urls, use_proxy=False):
        """Start checking M3U URLs"""
        if self.running:
            return False

        # Initialize check
        self.running = True
        self.total_checked = 0
        self.total_to_check = len(urls)
        self.good_results = []

        # Start stats tracking
        self.stats.start()

        # Log start of check
        logger.info(f"Starting M3U check with {len(urls)} URLs and {self.max_threads} threads")

        # Create threads
        threads = []
        for i in range(min(self.max_threads, len(urls))):
            t = threading.Thread(target=self.worker_thread, args=(urls, use_proxy))
            threads.append(t)
            t.daemon = True
            t.start()

        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_progress, args=(threads,))
        monitor_thread.daemon = True
        monitor_thread.start()

        return True

    def stop_check(self):
        """Stop checking"""
        if not self.running:
            return False

        logger.info("Stopping M3U check")
        self.running = False

        # Stop stats tracking and get summary
        stats_summary = self.stats.stop()
        logger.info(f"Check completed. Found {len(self.good_results)} valid M3U playlists out of {self.total_checked} checked.")
        logger.info(f"Check rate: {stats_summary['check_rate']:.2f} URLs/second, Duration: {stats_summary['duration_formatted']}")

        return True

    def worker_thread(self, urls, use_proxy):
        """Worker thread for checking URLs"""
        while self.running:
            # Get next URL to check
            with self.progress_lock:
                if self.total_checked >= len(urls):
                    break

                url_index = self.total_checked
                self.total_checked += 1

            url = urls[url_index].strip()

            # Check URL
            try:
                # Log the check
                logger.debug(f"Checking URL: {url}")

                # Perform the check
                result = self.check_m3u(url, use_proxy)

                # Record result in stats
                with self.results_lock:
                    self.stats.record_result(result)

                # Process result
                if result.get('is_valid', False):
                    with self.results_lock:
                        self.good_results.append(result)

                    # Save to file
                    if self.auto_save:
                        try:
                            with open('good_m3u.txt', 'a', encoding='utf-8') as f:
                                f.write(f"{url}\n")
                            logger.debug(f"Saved valid URL to file: {url}")
                        except Exception as e:
                            logger.error(f"Error saving to file: {str(e)}")

                    # Send to Telegram
                    if self.telegram_enabled and self.telegram_bot_token and self.telegram_chat_id:
                        self.send_to_telegram(result)

                # Emit result signal
                self.result_signal.emit(result)

                # Save error to file if needed
                self.save_error_to_file(result)
            except Exception as e:
                # Handle exceptions and still emit a result for failed checks
                error_result = {
                    'url': url,
                    'is_valid': False,
                    'error': str(e),
                    'status': 'Error',
                    'channels': 0,
                    'categories': 0,
                    'expires': 'Unknown'
                }
                self.result_signal.emit(error_result)
                logger.error(f"Error checking {url}: {str(e)}")

                # Record error in stats
                with self.results_lock:
                    self.stats.record_result(error_result)

            # Emit progress signal
            self.progress_signal.emit(self.total_checked, self.total_to_check)

            # Calculate check rate and estimated time remaining
            if self.total_checked % 10 == 0:  # Update every 10 checks
                stats = self.stats.get_summary()
                remaining = self.stats._estimate_time_remaining(self.total_to_check)
                logger.debug(f"Progress: {self.total_checked}/{self.total_to_check}, Rate: {stats['check_rate']:.2f}/s, ETA: {remaining}")

            # Small delay to prevent overwhelming servers
            time.sleep(0.1)

    def monitor_progress(self, threads):
        """Monitor progress and emit signals"""
        last_update_time = time.time()

        while self.running and any(t.is_alive() for t in threads):
            # Emit progress signal
            self.progress_signal.emit(self.total_checked, self.total_to_check)

            # Get stats for status message
            current_time = time.time()
            if current_time - last_update_time >= 1.0:  # Update status every second
                stats = self.stats.get_summary()
                remaining = self.stats._estimate_time_remaining(self.total_to_check)

                # Create detailed status message
                status_msg = (
                    f"Checking... {self.total_checked}/{self.total_to_check} "
                    f"({self.total_checked/self.total_to_check*100:.1f}%) | "
                    f"Rate: {stats['check_rate']:.1f}/s | "
                    f"Valid: {len(self.good_results)} | "
                    f"ETA: {remaining}"
                )

                # Emit status signal
                self.status_signal.emit(status_msg)
                last_update_time = current_time

            time.sleep(0.2)  # More frequent updates for smoother progress bar

        # Final progress update
        self.progress_signal.emit(self.total_checked, self.total_to_check)

        # Final status update
        if self.running:  # Only if not stopped manually
            stats = self.stats.get_summary()
            success_rate = len(self.good_results) / self.total_checked * 100 if self.total_checked > 0 else 0

            status_msg = (
                f"Check completed. Found {len(self.good_results)} valid M3U playlists "
                f"({success_rate:.1f}%) in {stats['duration_formatted']}."
            )

            self.status_signal.emit(status_msg)
            logger.info(status_msg)

        self.running = False

    def is_valid_m3u_content(self, content):
        """Check if content is a valid M3U file using multiple methods"""
        # Method 1: Check for #EXTM3U marker
        if '#EXTM3U' in content:
            return True

        # Method 2: Check for #EXTINF pattern which is common in M3U files
        if '#EXTINF' in content and ('http://' in content or 'https://' in content):
            return True

        # Method 3: Check for common M3U patterns
        if re.search(r'#EXT-X-STREAM-INF', content):
            return True

        # Method 4: Check for channel/stream URLs
        if re.search(r'#.+,.*\n(http|rtmp|rtsp)://', content):
            return True

        # Method 5: Check for any URL pattern after a line starting with #
        lines = content.split('\n')
        for i in range(len(lines) - 1):
            if lines[i].startswith('#') and (lines[i+1].startswith('http://') or lines[i+1].startswith('https://')):
                return True

        # Method 6: Check for specific IPTV patterns
        if 'EXTM3U' in content.upper() or 'EXTINF' in content.upper():
            return True

        # Method 7: Check for at least 5 URLs in the content
        url_count = len(re.findall(r'(https?://[^\s]+)', content))
        if url_count >= 5:
            return True

        return False

    def check_m3u(self, url, use_proxy=False):
        """Check if M3U URL is valid"""
        result = {
            'url': url,
            'is_valid': False,
            'status': 'Checking...',
            'channels': 0,
            'categories': 0,
            'expires': 'Unknown',
            'debug_info': ''  # Add debug info field
        }

        try:
            # Get proxy if needed
            proxies = None
            if use_proxy:
                proxy = self.get_next_proxy()
                proxies = format_proxy(proxy)

            # Create a session with retry capabilities
            session = RequestsSession(retries=3, backoff_factor=0.5, timeout=self.timeout)

            # Try different request methods
            try:
                # First try: normal GET request
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }

                # Make request with stream=True to handle large responses efficiently
                response = session.get(url, headers=headers, proxies=proxies, stream=True, verify=False)

                # If first attempt fails, try with different headers
                if response.status_code != 200:
                    logger.info(f"First attempt failed for {url} with status {response.status_code}, trying with different headers")
                    custom_headers = {
                        'User-Agent': 'VLC/3.0.16 LibVLC/3.0.16',
                        'Accept': '*/*',
                        'Connection': 'keep-alive'
                    }
                    response = session.get(url, headers=custom_headers, proxies=proxies, stream=True, verify=False)

                # Check response status
                if response.status_code != 200:
                    result['debug_info'] += f"HTTP status: {response.status_code}. "
                    result['status'] = f'HTTP Error: {response.status_code}'
                    # Save error to file
                    save_error_to_file(url, result['status'])
                    return result

                # Check content type
                content_type = response.headers.get('Content-Type', '').lower()
                if 'text/plain' in content_type or 'application/x-mpegurl' in content_type or 'audio/x-mpegurl' in content_type:
                    result['debug_info'] += f"Content-Type: {content_type} (good). "
                else:
                    result['debug_info'] += f"Content-Type: {content_type} (unusual but proceeding). "

                # Check if response is valid M3U
                content = ''
                # Read only first 100KB to check format
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=False):
                    if chunk:
                        try:
                            content += chunk.decode('utf-8', errors='ignore')
                        except:
                            # If decoding fails, try to decode as bytes
                            content += str(chunk)

                        if len(content) > 100000:  # 100KB is enough to check format
                            break

                # Check if it's a valid M3U file using multiple methods
                if self.is_valid_m3u_content(content):
                    result['is_valid'] = True
                    result['status'] = 'Active'

                    # Count channels
                    channels = content.count('#EXTINF')
                    if channels == 0:
                        # Try alternative counting method
                        channels = len(re.findall(r'(https?://[^\s]+)', content))

                    result['channels'] = channels
                    result['debug_info'] += f"Found {channels} channels. "

                    # Extract categories
                    categories = set()
                    for line in content.split('\n'):
                        if 'group-title=' in line:
                            match = re.search(r'group-title="([^"]+)"', line)
                            if match:
                                categories.add(match.group(1))

                    result['categories'] = len(categories)
                    result['debug_info'] += f"Found {len(categories)} categories. "

                    # Check for expiration date
                    for line in content.split('\n'):
                        if 'tvg-name=' in line and ('expires' in line.lower() or 'expiry' in line.lower()):
                            match = re.search(r'(expires|expiry)[:\s]+(\d{2}/\d{2}/\d{4}|\d{4}-\d{2}-\d{2})', line, re.IGNORECASE)
                            if match:
                                result['expires'] = match.group(2)

                                # Check if expired
                                try:
                                    # Parse date
                                    if '/' in match.group(2):
                                        expiry_date = datetime.strptime(match.group(2), '%d/%m/%Y')
                                    else:
                                        expiry_date = datetime.strptime(match.group(2), '%Y-%m-%d')

                                    # Check if expired
                                    if expiry_date < datetime.now():
                                        result['status'] = 'Expired'

                                    # Check if expired in 2024 or earlier
                                    if expiry_date.year <= 2024:
                                        result['status'] = 'Expired (2024 or earlier)'
                                        result['is_valid'] = False  # Mark as invalid if expired in 2024 or earlier
                                except:
                                    pass
                else:
                    result['status'] = 'Invalid Format'
                    result['debug_info'] += "Content doesn't match M3U patterns. "

                    # Add first few lines of content for debugging
                    first_lines = '\n'.join(content.split('\n')[:5])
                    result['debug_info'] += f"First lines: {first_lines[:200]}... "

            except Exception as e:
                import requests  # For exception types
                if isinstance(e, requests.exceptions.Timeout):
                    result['status'] = 'Timeout'
                    result['debug_info'] += f"Request timed out: {str(e)}. "
                elif isinstance(e, requests.exceptions.ConnectionError):
                    result['status'] = 'Connection Error'
                    result['debug_info'] += f"Connection error: {str(e)}. "
                    # Save connection errors to file
                    save_error_to_file(url, result['status'])
                elif isinstance(e, requests.exceptions.RequestException):
                    result['status'] = f'Request Error: {str(e)[:50]}'
                    result['debug_info'] += f"Request error: {str(e)}. "
                else:
                    result['status'] = f'Error: {str(e)[:50]}'
                    result['debug_info'] += f"General error: {str(e)}. "

                logger.error(f"Error checking M3U {url}: {str(e)}")

            finally:
                # Always close the session to free resources
                session.close()

        except Exception as e:
            result['status'] = f'Error: {str(e)[:50]}'
            result['debug_info'] += f"Exception outside request block: {str(e)}. "
            logger.error(f"Exception outside request block for {url}: {str(e)}")

        # Always add to results table even if not valid
        if not result['is_valid'] and 'Error' not in result['status'] and 'Timeout' not in result['status']:
            result['status'] = 'Invalid'

        # For debugging purposes, log the debug info
        logger.debug(f"Debug for {url}: {result['debug_info']}")

        return result

    def save_error_to_file(self, result):
        """Save error result to ERROR file"""
        try:
            # Check if the status contains error keywords
            status = result.get('status', '')
            if 'HTTP Error' in status or 'Connection Error' in status:
                save_error_to_file(result['url'], status)
                logger.info(f"Saved error to ERROR.txt: {result['url']} | {status}")
        except Exception as e:
            logger.error(f"Error saving to ERROR file: {str(e)}")

    def send_to_telegram(self, result):
        """Send result to Telegram"""
        # Skip if expired in 2024 or earlier
        if 'Expired (2024 or earlier)' in result.get('status', ''):
            return

        try:
            message = f"🟢 *Valid M3U Found*\n\n"
            message += f"🔗 URL: `{result['url']}`\n"
            message += f"📺 Channels: {result['channels']}\n"
            message += f"🏷️ Categories: {result['categories']}\n"
            message += f"⏱️ Expires: {result['expires']}\n"
            message += f"📊 Status: {result['status']}\n"

            success = send_to_telegram(
                self.telegram_bot_token,
                self.telegram_chat_id,
                message,
                "Markdown"
            )

            if not success:
                logger.error(f"Failed to send to Telegram: {result['url']}")
        except Exception as e:
            logger.error(f"Error sending to Telegram: {str(e)}")
