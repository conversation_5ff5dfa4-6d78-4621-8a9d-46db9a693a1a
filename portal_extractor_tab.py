import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QCheckBox, QSpinBox, QFileDialog, QProgressBar,
                            QGroupBox, QMessageBox, QRadioButton, QButtonGroup)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt

from portal_extractor import PortalExtractor

class PortalExtractorTab(QWidget):
    """Tab for Portal Extraction functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.extractor = PortalExtractor()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Portal Extraction Configuration")
        config_layout = QVBoxLayout()

        # Extraction method
        method_group = QGroupBox("Extraction Method")
        method_layout = QVBoxLayout()

        self.method_group = QButtonGroup(self)
        self.online_method = QRadioButton("Search Online for Latest Portal Lists")
        self.file_method = QRadioButton("Extract from File")
        self.directory_method = QRadioButton("Extract from Directory")
        self.online_method.setChecked(True)

        self.method_group.addButton(self.online_method)
        self.method_group.addButton(self.file_method)
        self.method_group.addButton(self.directory_method)

        method_layout.addWidget(self.online_method)
        method_layout.addWidget(self.file_method)
        method_layout.addWidget(self.directory_method)

        method_group.setLayout(method_layout)
        config_layout.addWidget(method_group)

        # File input (for file method)
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("File:"))
        self.file_input = QLineEdit()
        self.file_input.setPlaceholderText("Select a file with URL:login:pass format")
        self.file_input.setEnabled(False)
        file_layout.addWidget(self.file_input)

        self.browse_file_button = QPushButton("Browse")
        self.browse_file_button.clicked.connect(self.browse_file)
        self.browse_file_button.setEnabled(False)
        file_layout.addWidget(self.browse_file_button)

        config_layout.addLayout(file_layout)

        # Directory input (for directory method)
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("Directory:"))
        self.dir_input = QLineEdit()
        self.dir_input.setPlaceholderText("Select a directory containing .txt files")
        self.dir_input.setEnabled(False)
        dir_layout.addWidget(self.dir_input)

        self.browse_dir_button = QPushButton("Browse")
        self.browse_dir_button.clicked.connect(self.browse_directory)
        self.browse_dir_button.setEnabled(False)
        dir_layout.addWidget(self.browse_dir_button)

        config_layout.addLayout(dir_layout)

        # Filter options
        filter_group = QGroupBox("Filter Options")
        filter_layout = QVBoxLayout()

        self.iptv_filter = QCheckBox("Only IPTV related URLs")
        self.iptv_filter.setChecked(True)
        filter_layout.addWidget(self.iptv_filter)

        keywords_layout = QHBoxLayout()
        keywords_layout.addWidget(QLabel("Keywords:"))
        self.keywords_input = QLineEdit()
        self.keywords_input.setText("iptv,mac,xtream,/c/,stalker,portal,m3u,stb,ministra")
        self.keywords_input.setToolTip("Comma-separated keywords to filter URLs")
        keywords_layout.addWidget(self.keywords_input)
        filter_layout.addLayout(keywords_layout)

        # Advanced search options
        advanced_layout = QHBoxLayout()
        advanced_layout.addWidget(QLabel("Advanced Search:"))
        self.advanced_search = QCheckBox("Enable")
        self.advanced_search.setChecked(True)
        advanced_layout.addWidget(self.advanced_search)
        filter_layout.addLayout(advanced_layout)

        filter_group.setLayout(filter_layout)
        config_layout.addWidget(filter_group)

        # Proxy options
        proxy_group = QGroupBox("Proxy Settings")
        proxy_layout = QVBoxLayout()

        # Enable/disable proxy
        proxy_enable_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        self.use_proxy.setChecked(False)
        self.use_proxy.toggled.connect(self.toggle_proxy_settings)
        proxy_enable_layout.addWidget(self.use_proxy)
        proxy_layout.addLayout(proxy_enable_layout)

        # Proxy type
        proxy_type_layout = QHBoxLayout()
        proxy_type_layout.addWidget(QLabel("Type:"))
        self.proxy_type = QLineEdit()
        self.proxy_type.setPlaceholderText("http, socks4, socks5")
        self.proxy_type.setText("http")
        self.proxy_type.setEnabled(False)
        proxy_type_layout.addWidget(self.proxy_type)
        proxy_layout.addLayout(proxy_type_layout)

        # Proxy address
        proxy_address_layout = QHBoxLayout()
        proxy_address_layout.addWidget(QLabel("Address:"))
        self.proxy_address = QLineEdit()
        self.proxy_address.setPlaceholderText("IP:Port or hostname:port")
        self.proxy_address.setEnabled(False)
        proxy_address_layout.addWidget(self.proxy_address)
        proxy_layout.addLayout(proxy_address_layout)

        # Proxy authentication
        proxy_auth_layout = QHBoxLayout()
        self.proxy_auth = QCheckBox("Authentication")
        self.proxy_auth.setChecked(False)
        self.proxy_auth.setEnabled(False)
        self.proxy_auth.toggled.connect(self.toggle_proxy_auth)
        proxy_auth_layout.addWidget(self.proxy_auth)
        proxy_layout.addLayout(proxy_auth_layout)

        # Proxy username/password
        proxy_user_layout = QHBoxLayout()
        proxy_user_layout.addWidget(QLabel("Username:"))
        self.proxy_username = QLineEdit()
        self.proxy_username.setEnabled(False)
        proxy_user_layout.addWidget(self.proxy_username)
        proxy_layout.addLayout(proxy_user_layout)

        proxy_pass_layout = QHBoxLayout()
        proxy_pass_layout.addWidget(QLabel("Password:"))
        self.proxy_password = QLineEdit()
        self.proxy_password.setEchoMode(QLineEdit.Password)
        self.proxy_password.setEnabled(False)
        proxy_pass_layout.addWidget(self.proxy_password)
        proxy_layout.addLayout(proxy_pass_layout)

        proxy_group.setLayout(proxy_layout)
        config_layout.addWidget(proxy_group)

        # Connect radio buttons to enable/disable inputs
        self.online_method.toggled.connect(self.toggle_inputs)
        self.file_method.toggled.connect(self.toggle_inputs)
        self.directory_method.toggled.connect(self.toggle_inputs)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Extraction")
        self.start_button.clicked.connect(self.start_extraction)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Extraction")
        self.stop_button.clicked.connect(self.stop_extraction)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        self.export_button = QPushButton("Export Results")
        self.export_button.clicked.connect(self.export_results)
        action_layout.addWidget(self.export_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 3)
        self.results_table.setHorizontalHeaderLabels(["Portal URL", "Source", "Status"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from extractor to UI"""
        self.extractor.result_signal.connect(self.add_result)
        self.extractor.progress_signal.connect(self.update_progress)
        self.extractor.status_signal.connect(self.update_status)

        # Update extractor settings when UI changes
        self.thread_count.valueChanged.connect(self.update_extractor_settings)
        self.timeout.valueChanged.connect(self.update_extractor_settings)

    def update_extractor_settings(self):
        """Update extractor settings from UI"""
        self.extractor.max_threads = self.thread_count.value()
        self.extractor.timeout = self.timeout.value()

        # Update proxy settings
        self.extractor.proxies = self.get_proxy_settings()

        # Update advanced search settings
        self.extractor.use_advanced_search = self.advanced_search.isChecked()

    def toggle_inputs(self):
        """Enable/disable inputs based on selected method"""
        file_method_selected = self.file_method.isChecked()
        dir_method_selected = self.directory_method.isChecked()

        # Enable/disable file input
        self.file_input.setEnabled(file_method_selected)
        self.browse_file_button.setEnabled(file_method_selected)

        # Enable/disable directory input
        self.dir_input.setEnabled(dir_method_selected)
        self.browse_dir_button.setEnabled(dir_method_selected)

    def toggle_proxy_settings(self, enabled):
        """Enable/disable proxy settings based on checkbox state"""
        self.proxy_type.setEnabled(enabled)
        self.proxy_address.setEnabled(enabled)
        self.proxy_auth.setEnabled(enabled)

        # Also toggle auth fields if auth is checked
        auth_enabled = enabled and self.proxy_auth.isChecked()
        self.proxy_username.setEnabled(auth_enabled)
        self.proxy_password.setEnabled(auth_enabled)

    def toggle_proxy_auth(self, enabled):
        """Enable/disable proxy authentication fields"""
        if self.use_proxy.isChecked():
            self.proxy_username.setEnabled(enabled)
            self.proxy_password.setEnabled(enabled)

    def get_proxy_settings(self):
        """Get proxy settings as a dictionary for requests library"""
        if not self.use_proxy.isChecked() or not self.proxy_address.text().strip():
            return None

        proxy_type = self.proxy_type.text().strip().lower()
        proxy_address = self.proxy_address.text().strip()

        # Format: {protocol: proxy_url}
        proxies = {}

        # Handle different proxy types
        if proxy_type == "http":
            if not proxy_address.startswith("http://"):
                proxy_address = f"http://{proxy_address}"
            proxies["http"] = proxy_address
            proxies["https"] = proxy_address
        elif proxy_type == "socks4" or proxy_type == "socks5":
            # For SOCKS proxies, we need to format differently
            if self.proxy_auth.isChecked() and self.proxy_username.text() and self.proxy_password.text():
                username = self.proxy_username.text()
                password = self.proxy_password.text()
                proxy_url = f"{proxy_type}://{username}:{password}@{proxy_address}"
            else:
                proxy_url = f"{proxy_type}://{proxy_address}"

            proxies["http"] = proxy_url
            proxies["https"] = proxy_url

        return proxies

    def browse_file(self):
        """Browse for file with portal information"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Portal List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            self.file_input.setText(file_path)

    def browse_directory(self):
        """Browse for directory containing text files"""
        directory = QFileDialog.getExistingDirectory(self, "Select Directory")
        if directory:
            self.dir_input.setText(directory)

    def start_extraction(self):
        """Start portal extraction"""
        try:
            self.results_table.setRowCount(0)
            self.update_extractor_settings()

            # Get filter keywords
            filter_enabled = self.iptv_filter.isChecked()
            keywords = self.keywords_input.text().strip().split(',') if filter_enabled else []
            self.extractor.filter_keywords = keywords
            self.extractor.filter_enabled = filter_enabled

            # Disable buttons during extraction
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)

            if self.online_method.isChecked():
                # Search online for portals
                self.status_label.setText("Searching for portals online...")

                try:
                    # Run in a safer way
                    portals = self.extractor.search_portals_online()

                    if portals:
                        self.status_label.setText(f"Found {len(portals)} potential portals. Starting verification...")
                        self.extractor.start_verification(portals)
                        self.progress_bar.setMaximum(len(portals))
                        self.progress_bar.setValue(0)
                    else:
                        self.status_label.setText("No portals found online.")
                        self.start_button.setEnabled(True)
                        self.stop_button.setEnabled(False)
                except Exception as e:
                    self.status_label.setText(f"Error searching online: {str(e)}")
                    QMessageBox.warning(self, "Error", f"An error occurred while searching online: {str(e)}")
                    self.start_button.setEnabled(True)
                    self.stop_button.setEnabled(False)

            elif self.file_method.isChecked():
                # Extract from file
                file_path = self.file_input.text().strip()
                if not file_path:
                    QMessageBox.warning(self, "Warning", "Please select a file.")
                    self.start_button.setEnabled(True)
                    self.stop_button.setEnabled(False)
                    return

                try:
                    self.status_label.setText(f"Extracting portals from file: {file_path}")
                    portals = self.extractor.extract_from_file(file_path)

                    if portals:
                        self.status_label.setText(f"Extracted {len(portals)} potential portals from file. Starting verification...")
                        self.extractor.start_verification(portals)
                        self.progress_bar.setMaximum(len(portals))
                        self.progress_bar.setValue(0)
                    else:
                        self.status_label.setText("No portals extracted from file.")
                        self.start_button.setEnabled(True)
                        self.stop_button.setEnabled(False)
                except Exception as e:
                    self.status_label.setText(f"Error extracting from file: {str(e)}")
                    QMessageBox.warning(self, "Error", f"An error occurred while extracting from file: {str(e)}")
                    self.start_button.setEnabled(True)
                    self.stop_button.setEnabled(False)

            elif self.directory_method.isChecked():
                # Extract from directory
                directory_path = self.dir_input.text().strip()
                if not directory_path:
                    QMessageBox.warning(self, "Warning", "Please select a directory.")
                    self.start_button.setEnabled(True)
                    self.stop_button.setEnabled(False)
                    return

                try:
                    self.status_label.setText(f"Extracting portals from directory: {directory_path}")
                    portals = self.extractor.extract_from_directory(directory_path)

                    if portals:
                        self.status_label.setText(f"Extracted {len(portals)} potential portals from directory. Starting verification...")
                        self.extractor.start_verification(portals)
                        self.progress_bar.setMaximum(len(portals))
                        self.progress_bar.setValue(0)
                    else:
                        self.status_label.setText("No portals extracted from directory.")
                        self.start_button.setEnabled(True)
                        self.stop_button.setEnabled(False)
                except Exception as e:
                    self.status_label.setText(f"Error extracting from directory: {str(e)}")
                    QMessageBox.warning(self, "Error", f"An error occurred while extracting from directory: {str(e)}")
                    self.start_button.setEnabled(True)
                    self.stop_button.setEnabled(False)

        except Exception as e:
            self.status_label.setText(f"Error starting extraction: {str(e)}")
            QMessageBox.critical(self, "Error", f"An unexpected error occurred: {str(e)}")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

    def stop_extraction(self):
        """Stop portal extraction"""
        self.extractor.stop_verification()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Extraction stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def export_results(self):
        """Export results to file"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No results to export.")
            return

        file_path, _ = QFileDialog.getSaveFileName(self, "Save Portal List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    for row in range(self.results_table.rowCount()):
                        url = self.results_table.item(row, 0).text()
                        f.write(f"{url}\n")

                QMessageBox.information(self, "Success", f"Exported {self.results_table.rowCount()} portals to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {str(e)}")

    def add_result(self, result):
        """Add result to table"""
        # Skip if result is already in the table
        for row in range(self.results_table.rowCount()):
            if self.results_table.item(row, 0).text() == result.get('url', ''):
                return

        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('url', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('source', '')))

        status = "Verified" if result.get('is_portal', False) else "Potential"
        self.results_table.setItem(row, 2, QTableWidgetItem(status))

        # Highlight verified portals
        if result.get('is_portal', False):
            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                if item:
                    item.setBackground(QColor(200, 255, 200))  # Light green

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            # Count verified portals
            verified_count = 0
            for row in range(self.results_table.rowCount()):
                if self.results_table.item(row, 2).text() == "Verified":
                    verified_count += 1

            self.status_label.setText(f"Extraction completed. Found {verified_count} verified portals out of {self.results_table.rowCount()} potential portals.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)
