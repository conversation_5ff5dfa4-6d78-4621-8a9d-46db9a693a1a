import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QCheckBox, QSpinBox, QFileDialog, QProgressBar,
                            QGroupBox, QMessageBox)
from PyQt5.QtGui import QColor, QIcon
from PyQt5.QtCore import Qt

class AboutTab(QWidget):
    """Tab for About & Activation information"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # About section
        about_group = QGroupBox("About IPTV Tools")
        about_layout = QVBoxLayout()

        # App title and version
        title_label = QLabel("Twins-Cracking IPTV By PS-DRAGON")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = title_label.font()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        about_layout.addWidget(title_label)

        version_label = QLabel("Version 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        about_layout.addWidget(version_label)

        # Description
        description = QTextEdit()
        description.setReadOnly(True)
        description.setHtml("""
        <p style='text-align:center;'>A comprehensive tool for IPTV management and testing.</p>
        <p><b>Features:</b></p>
        <ul>
            <li>MAC Scanner: Scan MAC addresses to find active ones</li>
            <li>User-Pass Checker: Check username and password combinations</li>
            <li>M3U Checker: Verify M3U playlist URLs</li>
            <li>Xtream Code Checker: Check Xtream Code credentials</li>
            <li>XUI Panel Checker: Check XUI panel admin credentials</li>
            <li>Portal Extractor: Extract and verify IPTV portals</li>
            <li>Results Management: Save, export, and manage results</li>
        </ul>
        <p><b>Additional Features:</b></p>
        <ul>
            <li>Multi-threading support for faster scanning</li>
            <li>Proxy support for all checkers</li>
            <li>Automatic saving of good results during scanning</li>
            <li>Telegram integration for sending results to a Telegram bot</li>
            <li>Date filtering to exclude expired subscriptions</li>
        </ul>
        <p style='text-align:center;'><b>© 2025 PS-DRAGON. All rights reserved.</b></p>
        <p style='text-align:center;'>This software is provided for educational purposes only.<br>Use responsibly and in accordance with all applicable laws and regulations.</p>
        """)
        about_layout.addWidget(description)

        about_group.setLayout(about_layout)
        layout.addWidget(about_group)

        # Activation section
        activation_group = QGroupBox("Activation")
        activation_layout = QVBoxLayout()

        # License status
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("Status:"))
        status_value = QLabel("Activated")
        status_value.setStyleSheet("color: green; font-weight: bold;")
        status_layout.addWidget(status_value)
        status_layout.addStretch()
        activation_layout.addLayout(status_layout)

        # License key
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("License Key:"))
        key_value = QLineEdit("XXXX-XXXX-XXXX-XXXX")
        key_value.setReadOnly(True)
        key_layout.addWidget(key_value)
        activation_layout.addLayout(key_layout)

        # Activation buttons
        button_layout = QHBoxLayout()
        activate_button = QPushButton("Activate License")
        activate_button.clicked.connect(self.show_activation_dialog)
        button_layout.addWidget(activate_button)

        deactivate_button = QPushButton("Deactivate License")
        deactivate_button.clicked.connect(self.show_deactivation_dialog)
        button_layout.addWidget(deactivate_button)

        button_layout.addStretch()
        activation_layout.addLayout(button_layout)

        activation_group.setLayout(activation_layout)
        layout.addWidget(activation_group)

        # Contact section
        contact_group = QGroupBox("Contact & Support")
        contact_layout = QVBoxLayout()

        contact_info = QTextEdit()
        contact_info.setReadOnly(True)
        contact_info.setHtml("""
        <p><b>Official Telegram Bot:</b> @twinskeybot</p>
        <p><b>Email:</b> <EMAIL></p>
        <p><b>Website:</b> <a href='https://twins-cracking.com'>https://twins-cracking.com</a></p>
        <p>For support, bug reports, or feature requests, please contact us through our official Telegram bot.</p>
        """)
        contact_layout.addWidget(contact_info)

        contact_group.setLayout(contact_layout)
        layout.addWidget(contact_group)

        self.setLayout(layout)

    def show_activation_dialog(self):
        """Show activation dialog"""
        QMessageBox.information(self, "Activation", "This feature will be implemented in a future version.")

    def show_deactivation_dialog(self):
        """Show deactivation dialog"""
        QMessageBox.information(self, "Deactivation", "This feature will be implemented in a future version.")
