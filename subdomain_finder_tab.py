"""
Subdomain Finder Tab for IPTV Tools.
This module provides a UI tab for discovering subdomains of a given domain.
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QFileDialog, QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QGroupBox, QCheckBox, QMessageBox, QLineEdit,
                            QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

from subdomain_finder import SubdomainFinder

class SubdomainFinderTab(QWidget):
    """Tab for Subdomain Finder functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.finder = SubdomainFinder()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize UI components"""
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("الإعدادات")
        config_layout = QVBoxLayout()

        # Domain input
        domain_layout = QHBoxLayout()
        domain_layout.addWidget(QLabel("الدومين:"))
        self.domain_input = QLineEdit()
        self.domain_input.setPlaceholderText("example.com")
        domain_layout.addWidget(self.domain_input)
        config_layout.addLayout(domain_layout)

        # Discovery methods
        methods_group = QGroupBox("طرق البحث")
        methods_layout = QHBoxLayout()

        self.brute_check = QCheckBox("Brute Force")
        self.brute_check.setChecked(True)
        methods_layout.addWidget(self.brute_check)

        self.crtsh_check = QCheckBox("Certificate Transparency (crt.sh)")
        self.crtsh_check.setChecked(True)
        methods_layout.addWidget(self.crtsh_check)

        methods_group.setLayout(methods_layout)
        config_layout.addWidget(methods_group)

        # Advanced options
        advanced_group = QGroupBox("خيارات متقدمة")
        advanced_layout = QVBoxLayout()

        # Wordlist selection
        wordlist_layout = QHBoxLayout()
        wordlist_layout.addWidget(QLabel("قائمة الكلمات:"))
        self.wordlist_label = QLabel("القائمة الافتراضية")
        self.wordlist_label.setStyleSheet("font-weight: bold;")
        wordlist_layout.addWidget(self.wordlist_label)

        self.browse_wordlist_button = QPushButton("تصفح...")
        self.browse_wordlist_button.clicked.connect(self.browse_wordlist)
        wordlist_layout.addWidget(self.browse_wordlist_button)

        self.reset_wordlist_button = QPushButton("إعادة تعيين")
        self.reset_wordlist_button.clicked.connect(self.reset_wordlist)
        wordlist_layout.addWidget(self.reset_wordlist_button)

        advanced_layout.addLayout(wordlist_layout)

        # Thread and timeout settings
        settings_layout = QHBoxLayout()
        
        settings_layout.addWidget(QLabel("عدد الخيوط:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(20)
        settings_layout.addWidget(self.thread_count)

        settings_layout.addWidget(QLabel("مهلة الاتصال (ثوان):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        settings_layout.addWidget(self.timeout)

        advanced_layout.addLayout(settings_layout)
        advanced_group.setLayout(advanced_layout)
        config_layout.addWidget(advanced_group)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Action buttons
        action_layout = QHBoxLayout()

        self.start_button = QPushButton("بدء البحث")
        self.start_button.clicked.connect(self.start_scan)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("إيقاف البحث")
        self.stop_button.clicked.connect(self.stop_scan)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.save_button = QPushButton("حفظ النتائج")
        self.save_button.clicked.connect(self.save_results)
        self.save_button.setEnabled(False)
        action_layout.addWidget(self.save_button)

        self.clear_button = QPushButton("مسح النتائج")
        self.clear_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("جاهز")
        layout.addWidget(self.status_label)

        # Results text area
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        layout.addWidget(self.results_text)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from finder to UI"""
        self.finder.progress_signal.connect(self.update_progress)
        self.finder.status_signal.connect(self.update_status)
        self.finder.result_signal.connect(self.add_result)
        self.finder.finished_signal.connect(self.scan_finished)

    def browse_wordlist(self):
        """Browse for wordlist file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "اختر ملف قائمة الكلمات", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            if self.finder.load_wordlist(file_path):
                self.wordlist_label.setText(os.path.basename(file_path))
            else:
                QMessageBox.warning(self, "تحذير", "فشل تحميل ملف قائمة الكلمات.")

    def reset_wordlist(self):
        """Reset to default wordlist"""
        self.finder.wordlist = self.finder.DEFAULT_WORDLIST
        self.wordlist_label.setText("القائمة الافتراضية")
        self.status_label.setText(f"تم إعادة تعيين قائمة الكلمات إلى القائمة الافتراضية ({len(self.finder.wordlist)} كلمة)")

    def start_scan(self):
        """Start subdomain discovery"""
        domain = self.domain_input.text().strip()
        if not domain:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال اسم الدومين.")
            return

        # Get selected methods
        methods = []
        if self.brute_check.isChecked():
            methods.append('brute')
        if self.crtsh_check.isChecked():
            methods.append('crtsh')

        if not methods:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار طريقة بحث واحدة على الأقل.")
            return

        # Clear previous results
        self.clear_results()

        # Start scan
        success = self.finder.start_scan(
            domain,
            methods=methods,
            threads=self.thread_count.value(),
            timeout=self.timeout.value()
        )

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.save_button.setEnabled(False)
            self.status_label.setText(f"جاري البحث عن الساب دومينز لـ: {domain}")
        else:
            QMessageBox.critical(self, "خطأ", "فشل بدء البحث.")

    def stop_scan(self):
        """Stop subdomain discovery"""
        self.finder.stop_scan()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.save_button.setEnabled(True)
        self.status_label.setText("تم إيقاف البحث")

    def clear_results(self):
        """Clear results"""
        self.results_text.clear()
        self.progress_bar.setValue(0)
        self.status_label.setText("جاهز")

    def add_result(self, subdomain):
        """Add subdomain to results"""
        self.results_text.append(subdomain)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

    def scan_finished(self, subdomains):
        """Handle scan completion"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.save_button.setEnabled(True)
        
        if subdomains:
            self.status_label.setText(f"اكتمل البحث. تم العثور على {len(subdomains)} ساب دومين.")
            QMessageBox.information(
                self,
                "اكتمل البحث",
                f"تم العثور على {len(subdomains)} ساب دومين.\n"
                f"تم حفظ النتائج في الملف: {self.finder.output_file}"
            )
        else:
            self.status_label.setText("اكتمل البحث. لم يتم العثور على أي ساب دومين.")

    def save_results(self):
        """Save results to custom file"""
        if not self.results_text.toPlainText():
            QMessageBox.warning(self, "تحذير", "لا توجد نتائج للحفظ.")
            return

        file_path, _ = QFileDialog.getSaveFileName(self, "حفظ النتائج", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.results_text.toPlainText())
                self.status_label.setText(f"تم حفظ النتائج في: {file_path}")
                QMessageBox.information(self, "تم الحفظ", f"تم حفظ النتائج بنجاح في:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل حفظ النتائج: {str(e)}")
