"""
Utility functions and classes for the IPTV Checker application.
This module contains shared functionality used across different checkers.
"""

import requests
import time
import logging
import os
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("checker.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("iptv_checker")

# Ensure ERROR.txt exists
if not os.path.exists('ERROR.txt'):
    with open('ERROR.txt', 'w', encoding='utf-8') as f:
        f.write("# IPTV Checker Error Log\n")
        f.write("# Format: URL | Error Type\n")
        f.write("# Created: {}\n\n".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

class RequestsSession:
    """
    A wrapper around requests.Session that provides connection pooling,
    automatic retries, and other performance improvements.
    """
    
    def __init__(self, retries=3, backoff_factor=0.3, 
                 status_forcelist=(500, 502, 504), timeout=10):
        """
        Initialize a new session with retry capabilities.
        
        Args:
            retries: Number of retries for failed requests
            backoff_factor: Backoff factor for retries (time = backoff_factor * (2 ** (retry - 1)))
            status_forcelist: HTTP status codes that should trigger a retry
            timeout: Default timeout for requests
        """
        self.session = requests.Session()
        self.timeout = timeout
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
            allowed_methods=["GET", "POST"]
        )
        
        # Mount the adapter to the session
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        })
    
    def get(self, url, **kwargs):
        """
        Perform a GET request with the session.
        
        Args:
            url: The URL to request
            **kwargs: Additional arguments to pass to requests.get
            
        Returns:
            requests.Response object
        """
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        
        try:
            return self.session.get(url, **kwargs)
        except Exception as e:
            logger.error(f"Error in GET request to {url}: {str(e)}")
            raise
    
    def post(self, url, **kwargs):
        """
        Perform a POST request with the session.
        
        Args:
            url: The URL to request
            **kwargs: Additional arguments to pass to requests.post
            
        Returns:
            requests.Response object
        """
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        
        try:
            return self.session.post(url, **kwargs)
        except Exception as e:
            logger.error(f"Error in POST request to {url}: {str(e)}")
            raise
    
    def close(self):
        """Close the session."""
        self.session.close()

def format_proxy(proxy):
    """
    Format a proxy string into a dictionary for requests.
    
    Args:
        proxy: Proxy string in format "(Http)ip:port" or "(Socks5)ip:port"
        
    Returns:
        Dictionary with http/https keys or None if proxy is invalid
    """
    if not proxy:
        return None
        
    formatted_proxy = None
    
    try:
        if proxy.startswith('(Http)'):
            proxy_data = proxy[6:].split(':')
            if len(proxy_data) >= 2:
                proxy_url = f"http://{proxy_data[0]}:{proxy_data[1]}"
                if len(proxy_data) >= 4:  # With username and password
                    proxy_url = f"http://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                formatted_proxy = {"http": proxy_url, "https": proxy_url}
        elif proxy.startswith('(Socks5)'):
            proxy_data = proxy[8:].split(':')
            if len(proxy_data) >= 2:
                proxy_url = f"socks5://{proxy_data[0]}:{proxy_data[1]}"
                if len(proxy_data) >= 4:  # With username and password
                    proxy_url = f"socks5://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                formatted_proxy = {"http": proxy_url, "https": proxy_url}
    except Exception as e:
        logger.error(f"Error formatting proxy {proxy}: {str(e)}")
        return None
        
    return formatted_proxy

def save_error_to_file(url, status, additional_info=None):
    """
    Save error information to ERROR.txt file.
    
    Args:
        url: The URL that caused the error
        status: Error status (e.g., "HTTP Error: 404", "Connection Error")
        additional_info: Any additional information to include
    """
    try:
        if 'HTTP Error' in status or 'Connection Error' in status:
            with open('ERROR.txt', 'a', encoding='utf-8') as f:
                error_line = f"{url} | {status}"
                if additional_info:
                    error_line += f" | {additional_info}"
                f.write(f"{error_line}\n")
            logger.info(f"Saved error to ERROR.txt: {url} | {status}")
    except Exception as e:
        logger.error(f"Error saving to ERROR file: {str(e)}")

def send_to_telegram(bot_token, chat_id, message, parse_mode="Markdown"):
    """
    Send a message to Telegram.
    
    Args:
        bot_token: Telegram bot token
        chat_id: Telegram chat ID
        message: Message to send
        parse_mode: Message parse mode (Markdown or HTML)
        
    Returns:
        True if successful, False otherwise
    """
    if not bot_token or not chat_id:
        return False
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            "chat_id": chat_id,
            "text": message,
            "parse_mode": parse_mode
        }
        
        session = RequestsSession(retries=2, timeout=10)
        response = session.post(url, json=payload)
        session.close()
        
        return response.status_code == 200
    except Exception as e:
        logger.error(f"Error sending to Telegram: {str(e)}")
        return False
