"""
Simple launcher for the IPTV Tools application.
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout
from PyQt5.QtCore import Qt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("iptv_tools.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("iptv_tools")

def main():
    """Main entry point for the application"""
    logger.info("Starting IPTV Tools application")
    
    # Create application
    app = QApplication(sys.argv)
    
    # Create main window
    window = QMainWindow()
    window.setWindowTitle("IPTV Tools")
    window.setGeometry(100, 100, 1200, 800)
    
    # Create central widget
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    # Create layout
    layout = QVBoxLayout(central_widget)
    
    # Create tab widget
    tabs = QTabWidget()
    layout.addWidget(tabs)
    
    # Add a simple message
    info_label = QWidget()
    info_layout = QVBoxLayout(info_label)
    info_layout.addWidget(QWidget())  # Spacer
    
    from PyQt5.QtWidgets import QLabel
    message = QLabel("IPTV Tools is running!")
    message.setAlignment(Qt.AlignCenter)
    message.setStyleSheet("font-size: 24pt; font-weight: bold; color: #2c3e50;")
    info_layout.addWidget(message)
    
    description = QLabel("This is a simple launcher for the IPTV Tools application.")
    description.setAlignment(Qt.AlignCenter)
    description.setStyleSheet("font-size: 14pt; color: #7f8c8d;")
    info_layout.addWidget(description)
    
    info_layout.addWidget(QWidget())  # Spacer
    
    tabs.addTab(info_label, "Info")
    
    # Show window
    window.show()
    
    # Run application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
