"""
Full launcher for the IPTV Tools application.
This script attempts to load all available tabs from the application.
"""

import sys
import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                            QCheckBox, QSpinBox, QFileDialog, QProgressBar, QGroupBox,
                            QRadioButton, QMessageBox, QMenu, QAction, QSplashScreen)
from PyQt5.QtGui import QIcon, QColor, QFont, QPixmap
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("iptv_tools.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("iptv_tools")

class IPTVTools(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()

        # Set window properties
        self.setWindowTitle("IPTV Tools")
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # Create tab widget
        self.tabs = QTabWidget()
        self.main_layout.addWidget(self.tabs)

        # Try to load tabs
        self.load_tabs()

        # Initialize UI components
        self.init_ui()

        logger.info("Application UI initialized")

    def load_tabs(self):
        """Try to load all available tabs"""
        # Add a simple info tab
        info_tab = QWidget()
        info_layout = QVBoxLayout(info_tab)

        # Add logo and title
        title_layout = QHBoxLayout()

        # Add logo placeholder
        logo_label = QLabel()
        logo_label.setFixedSize(100, 100)
        logo_label.setStyleSheet("background-color: #3498db; border-radius: 50px;")
        title_layout.addWidget(logo_label)

        # Title and version
        title_info = QVBoxLayout()
        title_label = QLabel("IPTV Tools")
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #2c3e50;")
        title_info.addWidget(title_label)

        version_label = QLabel("Version 1.0.0")
        version_label.setStyleSheet("font-size: 12pt; color: #7f8c8d;")
        title_info.addWidget(version_label)

        title_layout.addLayout(title_info)
        title_layout.addStretch()

        info_layout.addLayout(title_layout)

        # Add description
        description = QLabel(
            "IPTV Tools is a comprehensive suite for managing and testing IPTV services."
        )
        description.setWordWrap(True)
        description.setStyleSheet("font-size: 12pt; color: #2c3e50; margin: 20px 0;")
        info_layout.addWidget(description)

        # Add features list
        features_group = QGroupBox("Features")
        features_layout = QVBoxLayout()

        features = [
            "MAC Scanner - Test MAC addresses against portals",
            "M3U Checker - Validate M3U playlists and extract channel information",
            "Xtream Code Checker - Test Xtream Code credentials",
            "XUI Panel Checker - Test XUI panel credentials",
            "Portal Extractor - Extract portal information",
            "Link Extractor - Extract links from websites"
        ]

        for feature in features:
            feature_label = QLabel("• " + feature)
            feature_label.setStyleSheet("font-size: 10pt; color: #2c3e50; margin: 2px 0;")
            features_layout.addWidget(feature_label)

        features_group.setLayout(features_layout)
        info_layout.addWidget(features_group)

        # Add stretch to push everything to the top
        info_layout.addStretch()

        # Add the info tab
        self.tabs.addTab(info_tab, "About")

        # Try to load other tabs
        try:
            # Try to import and add MAC Scanner tab
            try:
                from mac_tab import MACTab
                self.mac_scanner_tab = MACTab()
                self.tabs.addTab(self.mac_scanner_tab, "MAC SCANNER")
                logger.info("Added MAC Scanner tab")
            except Exception as e:
                logger.error(f"Failed to load MAC Scanner tab: {str(e)}")

            # Try to import and add M3U Checker tab
            try:
                from m3u_tab import M3UTab
                self.m3u_checker_tab = M3UTab()
                self.tabs.addTab(self.m3u_checker_tab, "M3U CHECKER")
                logger.info("Added M3U Checker tab")
            except Exception as e:
                logger.error(f"Failed to load M3U Checker tab: {str(e)}")

            # Try to import and add Xtream Code tab
            try:
                from xtream_code_tab import XtreamCodeTab
                self.xtream_code_tab = XtreamCodeTab()
                self.tabs.addTab(self.xtream_code_tab, "XTREAM CODE")
                logger.info("Added Xtream Code tab")
            except Exception as e:
                logger.error(f"Failed to load Xtream Code tab: {str(e)}")

            # Try to import and add XUI Panel tab
            try:
                from xui_panel_tab import XUIPanelTab
                self.xui_panel_tab = XUIPanelTab()
                self.tabs.addTab(self.xui_panel_tab, "XUI PANEL")
                logger.info("Added XUI Panel tab")
            except Exception as e:
                logger.error(f"Failed to load XUI Panel tab: {str(e)}")

            # Try to import and add Portal Extractor tab
            try:
                from portal_extractor_tab import PortalExtractorTab
                self.portal_extractor_tab = PortalExtractorTab()
                self.tabs.addTab(self.portal_extractor_tab, "PORTAL EXTRACTOR")
                logger.info("Added Portal Extractor tab")
            except Exception as e:
                logger.error(f"Failed to load Portal Extractor tab: {str(e)}")

            # Try to import and add Link Extractor tab
            try:
                from link_extractor_tab import LinkExtractorTab
                self.link_extractor_tab = LinkExtractorTab()
                self.tabs.addTab(self.link_extractor_tab, "LINK EXTRACTOR")
                logger.info("Added Link Extractor tab")
            except Exception as e:
                logger.error(f"Failed to load Link Extractor tab: {str(e)}")

        except Exception as e:
            logger.error(f"Error loading tabs: {str(e)}")

    def init_ui(self):
        """Initialize UI components"""
        # Create status bar
        self.statusBar().showMessage("Ready")

        # Create menu bar
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("File")

        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Help menu
        help_menu = menubar.addMenu("Help")

        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About", "IPTV Tools v1.0.0\n\nA comprehensive tool for IPTV management and testing.")

def main():
    """Main entry point for the application"""
    logger.info("Starting IPTV Tools application")

    # Create application
    app = QApplication(sys.argv)

    # Create splash screen
    splash_pixmap = QPixmap(200, 200)
    splash_pixmap.fill(QColor("#3498db"))
    splash = QSplashScreen(splash_pixmap)
    splash.showMessage("Loading IPTV Tools...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
    splash.show()
    app.processEvents()

    # Create and show main window
    window = IPTVTools()

    # Close splash screen after a delay
    QTimer.singleShot(2000, splash.close)
    QTimer.singleShot(2000, window.show)

    # Run application
    exit_code = app.exec_()
    logger.info(f"Application exiting with code {exit_code}")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
