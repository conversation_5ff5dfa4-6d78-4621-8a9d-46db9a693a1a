import sys
import os
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMain<PERSON>indow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                            QCheckBox, QSpinBox, QFileDialog, QProgressBar, QGroupBox,
                            QRadioButton, QMessageBox, QMenu, QAction)
from PyQt5.QtGui import QIcon, QColor, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer

from mac_scanner import MACScanner
from user_pass_checker import UserPassChecker
from m3u_checker import <PERSON>3UChecker
from xtream_code_checker import XtreamCodeChecker
from xui_panel_checker import XUIPanelChecker
from portal_extractor import PortalExtractor

class TelegramSettingsWidget(QWidget):
    """Widget for Telegram settings"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Telegram group
        telegram_group = QGroupBox("Telegram Settings")
        telegram_layout = QVBoxLayout()

        # Enable Telegram
        self.telegram_enabled = QCheckBox("Enable Telegram Notifications")
        telegram_layout.addWidget(self.telegram_enabled)

        # Option to send errors to Telegram
        self.send_errors = QCheckBox("Send Error Results to Telegram")
        self.send_errors.setChecked(False)  # Disabled by default
        telegram_layout.addWidget(self.send_errors)

        # Bot token
        token_layout = QHBoxLayout()
        token_layout.addWidget(QLabel("Bot Token:"))
        self.bot_token = QLineEdit()
        token_layout.addWidget(self.bot_token)
        telegram_layout.addLayout(token_layout)

        # Chat ID
        chat_id_layout = QHBoxLayout()
        chat_id_layout.addWidget(QLabel("Chat ID:"))
        self.chat_id = QLineEdit()
        chat_id_layout.addWidget(self.chat_id)
        telegram_layout.addLayout(chat_id_layout)

        # Test button
        self.test_button = QPushButton("Test Telegram Bot")
        self.test_button.clicked.connect(self.test_telegram)
        telegram_layout.addWidget(self.test_button)

        telegram_group.setLayout(telegram_layout)
        layout.addWidget(telegram_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        self.setLayout(layout)

    def test_telegram(self):
        """Test Telegram bot connection"""
        import requests

        if not self.telegram_enabled.isChecked():
            QMessageBox.warning(self, "Warning", "Telegram notifications are not enabled.")
            return

        token = self.bot_token.text().strip()
        chat_id = self.chat_id.text().strip()

        if not token or not chat_id:
            QMessageBox.warning(self, "Warning", "Please enter both Bot Token and Chat ID.")
            return

        try:
            message = "🧪 Test message from IPTV Tools by Manzera Ayenna"
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            payload = {
                "chat_id": chat_id,
                "text": message
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                QMessageBox.information(self, "Success", "Test message sent successfully!")
            else:
                data = response.json()
                QMessageBox.critical(self, "Error", f"Failed to send message: {data.get('description', 'Unknown error')}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to send message: {str(e)}")

class MACTab(QWidget):
    """Tab for MAC Scanner functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.scanner = MACScanner()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Portal input
        portal_layout = QHBoxLayout()
        portal_layout.addWidget(QLabel("Portal:"))
        self.portal_input = QLineEdit()
        self.portal_input.setPlaceholderText("http://example.com:8080")
        portal_layout.addWidget(self.portal_input)
        config_layout.addLayout(portal_layout)

        # MAC input
        mac_layout = QHBoxLayout()
        mac_layout.addWidget(QLabel("MAC:"))
        self.mac_input = QTextEdit()
        self.mac_input.setPlaceholderText("Enter MAC addresses, one per line")
        self.mac_input.setMaximumHeight(100)
        mac_layout.addWidget(self.mac_input)
        config_layout.addLayout(mac_layout)

        # Buttons for loading MACs
        buttons_layout = QHBoxLayout()
        self.load_mac_button = QPushButton("Load MAC List")
        self.load_mac_button.clicked.connect(self.load_mac_list)
        buttons_layout.addWidget(self.load_mac_button)

        self.clear_mac_button = QPushButton("Clear")
        self.clear_mac_button.clicked.connect(self.clear_mac_list)
        buttons_layout.addWidget(self.clear_mac_button)

        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        buttons_layout.addWidget(self.remove_duplicates_button)
        config_layout.addLayout(buttons_layout)

        # Proxy settings
        proxy_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        proxy_layout.addWidget(self.use_proxy)

        self.load_proxy_button = QPushButton("Load Proxy List")
        self.load_proxy_button.clicked.connect(self.load_proxy_list)
        proxy_layout.addWidget(self.load_proxy_button)

        self.proxy_count_label = QLabel("Proxies: 0")
        proxy_layout.addWidget(self.proxy_count_label)

        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # MAC Generator group
        mac_gen_group = QGroupBox("MAC Address Generator")
        mac_gen_layout = QVBoxLayout()

        # Prefix input
        prefix_layout = QHBoxLayout()
        prefix_layout.addWidget(QLabel("Fixed Prefix:"))
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("e.g., 00:1A:79:")
        self.prefix_input.setText("00:1A:79:")
        prefix_layout.addWidget(self.prefix_input)
        mac_gen_layout.addLayout(prefix_layout)

        # Count input
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("Number of MACs:"))
        self.mac_count = QSpinBox()
        self.mac_count.setMinimum(1)
        self.mac_count.setMaximum(100000)
        self.mac_count.setValue(10000)
        count_layout.addWidget(self.mac_count)

        # Generate button
        self.generate_mac_button = QPushButton("Generate MAC Addresses")
        self.generate_mac_button.clicked.connect(self.generate_mac_addresses)
        count_layout.addWidget(self.generate_mac_button)

        mac_gen_layout.addLayout(count_layout)
        mac_gen_group.setLayout(mac_gen_layout)
        layout.addWidget(mac_gen_group)

        # Telegram settings
        self.telegram_settings = TelegramSettingsWidget()
        layout.addWidget(self.telegram_settings)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Scan")
        self.start_button.clicked.connect(self.start_scan)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Scan")
        self.stop_button.clicked.connect(self.stop_scan)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["MAC", "Portal", "Status", "Created", "Expires"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from scanner to UI"""
        self.scanner.result_signal.connect(self.add_result)
        self.scanner.progress_signal.connect(self.update_progress)
        self.scanner.status_signal.connect(self.update_status)

        # Update scanner settings when UI changes
        self.thread_count.valueChanged.connect(self.update_scanner_settings)
        self.timeout.valueChanged.connect(self.update_scanner_settings)
        self.telegram_settings.telegram_enabled.toggled.connect(self.update_scanner_settings)
        self.telegram_settings.send_errors.toggled.connect(self.update_scanner_settings)
        self.telegram_settings.bot_token.textChanged.connect(self.update_scanner_settings)
        self.telegram_settings.chat_id.textChanged.connect(self.update_scanner_settings)

    def update_scanner_settings(self):
        """Update scanner settings from UI"""
        self.scanner.max_threads = self.thread_count.value()
        self.scanner.timeout = self.timeout.value()
        self.scanner.telegram_enabled = self.telegram_settings.telegram_enabled.isChecked()
        self.scanner.telegram_bot_token = self.telegram_settings.bot_token.text().strip()
        self.scanner.telegram_chat_id = self.telegram_settings.chat_id.text().strip()
        self.scanner.send_errors_to_telegram = self.telegram_settings.send_errors.isChecked()

    def load_mac_list(self):
        """Load MAC list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open MAC List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    macs = f.read()
                    self.mac_input.setText(macs)
                    self.status_label.setText(f"Loaded {len(macs.splitlines())} MACs from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load MAC list: {str(e)}")

    def clear_mac_list(self):
        """Clear MAC list"""
        self.mac_input.clear()

    def remove_duplicates(self):
        """Remove duplicate MAC addresses from the list"""
        mac_text = self.mac_input.toPlainText().strip()
        if not mac_text:
            return

        # Get unique MAC addresses while preserving order
        mac_list = [line.strip() for line in mac_text.splitlines() if line.strip()]
        unique_macs = []
        seen = set()
        for mac in mac_list:
            if mac.lower() not in seen:
                seen.add(mac.lower())
                unique_macs.append(mac)

        # Update the text area with unique MACs
        self.mac_input.setText("\n".join(unique_macs))

        # Show message about removed duplicates
        removed_count = len(mac_list) - len(unique_macs)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate MAC addresses")
        else:
            self.status_label.setText("No duplicate MAC addresses found")

    def load_proxy_list(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                count = self.scanner.load_proxies(file_path)
                self.proxy_count_label.setText(f"Proxies: {count}")
                self.status_label.setText(f"Loaded {count} proxies from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load proxy list: {str(e)}")

    def start_scan(self):
        """Start MAC scanning"""
        portal = self.portal_input.text().strip()
        if not portal:
            QMessageBox.warning(self, "Warning", "Please enter a portal URL.")
            return

        # Add http:// if missing
        if not portal.startswith('http://') and not portal.startswith('https://'):
            portal = f"http://{portal}"
            self.portal_input.setText(portal)

        # Get MAC list
        mac_text = self.mac_input.toPlainText().strip()
        if not mac_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one MAC address.")
            return

        mac_list = [line.strip() for line in mac_text.splitlines() if line.strip()]

        # Update scanner settings
        self.update_scanner_settings()

        # Start scanning
        success = self.scanner.start_scan(mac_list, portal, self.use_proxy.isChecked())

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Scanning...")
            self.progress_bar.setMaximum(len(mac_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start scanning.")

    def stop_scan(self):
        """Stop MAC scanning"""
        self.scanner.stop_scan()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Scan stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        status = result.get('status', '')

        # Add the result to the table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('mac', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('portal', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('created', '')))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('expires', '')))

        # Highlight row based on status
        if 'Active' in status:
            color = QColor(200, 255, 200)  # Light green for active
        elif 'Unauthorized' in status:
            color = QColor(255, 200, 200)  # Light red for unauthorized
        else:
            color = QColor(255, 255, 200)  # Light yellow for other statuses

        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(color)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(f"Scan completed. Found {self.results_table.rowCount()} active MACs.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

    def generate_mac_addresses(self):
        """Generate MAC addresses with the given prefix"""
        prefix = self.prefix_input.text().strip()
        count = self.mac_count.value()

        if not prefix:
            QMessageBox.warning(self, "Warning", "Please enter a MAC prefix.")
            return

        # Validate prefix format
        import re
        if not re.match(r'^([0-9A-F]{2}[:-]){1,5}$', prefix, re.IGNORECASE):
            QMessageBox.warning(self, "Warning", "Invalid MAC prefix format. Should be like 00:1A:79:")
            return

        # Generate MAC addresses
        import random
        mac_list = []

        # Determine how many parts are missing
        parts = prefix.split(':')
        if len(parts) > 6:
            QMessageBox.warning(self, "Warning", "Too many parts in MAC prefix.")
            return

        missing_parts = 6 - len(parts)

        # Generate the specified number of MAC addresses
        for _ in range(count):
            mac = prefix
            for i in range(missing_parts):
                if i > 0 or (prefix and prefix[-1] in ':-'):
                    mac += f"{random.randint(0, 255):02X}"
                else:
                    mac += f":{random.randint(0, 255):02X}"
                if i < missing_parts - 1:
                    mac += ":"
            mac_list.append(mac)

        # Add to the MAC input
        current_text = self.mac_input.toPlainText().strip()
        if current_text:
            self.mac_input.setText(current_text + "\n" + "\n".join(mac_list))
        else:
            self.mac_input.setText("\n".join(mac_list))

        self.status_label.setText(f"Generated {count} MAC addresses with prefix {prefix}")


class UserPassTab(QWidget):
    """Tab for User/Pass checking functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checker = UserPassChecker()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Portal input
        portal_layout = QHBoxLayout()
        portal_layout.addWidget(QLabel("Portal:"))
        self.portal_input = QLineEdit()
        self.portal_input.setPlaceholderText("http://example.com:8080")
        portal_layout.addWidget(self.portal_input)
        config_layout.addLayout(portal_layout)

        # Username/Password input
        creds_layout = QHBoxLayout()

        # Username
        username_layout = QVBoxLayout()
        username_layout.addWidget(QLabel("Username:"))
        self.username_input = QTextEdit()
        self.username_input.setPlaceholderText("Enter usernames, one per line")
        self.username_input.setMaximumHeight(100)
        username_layout.addWidget(self.username_input)
        creds_layout.addLayout(username_layout)

        # Password
        password_layout = QVBoxLayout()
        password_layout.addWidget(QLabel("Password:"))
        self.password_input = QTextEdit()
        self.password_input.setPlaceholderText("Enter passwords, one per line")
        self.password_input.setMaximumHeight(100)
        password_layout.addWidget(self.password_input)
        creds_layout.addLayout(password_layout)

        config_layout.addLayout(creds_layout)

        # Buttons for loading credentials
        buttons_layout = QHBoxLayout()
        self.load_creds_button = QPushButton("Load Credentials")
        self.load_creds_button.clicked.connect(self.load_credentials)
        buttons_layout.addWidget(self.load_creds_button)

        self.clear_creds_button = QPushButton("Clear")
        self.clear_creds_button.clicked.connect(self.clear_credentials)
        buttons_layout.addWidget(self.clear_creds_button)

        config_layout.addLayout(buttons_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Max Threads:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        thread_layout.addWidget(self.thread_count)

        thread_layout.addWidget(QLabel("Timeout (sec):"))
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Check")
        self.start_button.clicked.connect(self.start_check)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Check")
        self.stop_button.clicked.connect(self.stop_check)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 4)
        self.results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from checker to UI"""
        self.checker.result_signal.connect(self.add_result)
        self.checker.progress_signal.connect(self.update_progress)
        self.checker.status_signal.connect(self.update_status)

    def load_credentials(self):
        """Load credentials from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Credentials List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    lines = f.readlines()

                    # Try to parse credentials in format username:password
                    usernames = []
                    passwords = []

                    for line in lines:
                        line = line.strip()
                        if ':' in line:
                            parts = line.split(':', 1)
                            usernames.append(parts[0])
                            passwords.append(parts[1])

                    if usernames and passwords:
                        self.username_input.setText('\n'.join(usernames))
                        self.password_input.setText('\n'.join(passwords))
                        self.status_label.setText(f"Loaded {len(usernames)} credential pairs from {file_path}")
                    else:
                        # If no username:password format found, just load as usernames
                        self.username_input.setText('\n'.join([l.strip() for l in lines if l.strip()]))
                        self.status_label.setText(f"Loaded {len(lines)} usernames from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load credentials: {str(e)}")

    def clear_credentials(self):
        """Clear credentials"""
        self.username_input.clear()
        self.password_input.clear()

    def start_check(self):
        """Start credential checking"""
        portal = self.portal_input.text().strip()
        if not portal:
            QMessageBox.warning(self, "Warning", "Please enter a portal URL.")
            return

        # Add http:// if missing
        if not portal.startswith('http://') and not portal.startswith('https://'):
            portal = f"http://{portal}"
            self.portal_input.setText(portal)

        # Get username list
        username_text = self.username_input.toPlainText().strip()
        if not username_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one username.")
            return

        username_list = [line.strip() for line in username_text.splitlines() if line.strip()]

        # Get password list
        password_text = self.password_input.toPlainText().strip()
        password_list = [line.strip() for line in password_text.splitlines() if line.strip()]

        # If no passwords provided, use empty password
        if not password_list:
            password_list = [""]

        # Update checker settings
        self.checker.max_threads = self.thread_count.value()
        self.checker.timeout = self.timeout.value()

        # Start checking
        success = self.checker.start_check(username_list, password_list, portal)

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Checking...")
            self.progress_bar.setMaximum(len(username_list) * len(password_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start checking.")

    def stop_check(self):
        """Stop credential checking"""
        self.checker.stop_check()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Check stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        # Add the result to the table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('portal', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('username', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(result.get('password', '')))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('status', '')))

        # Highlight row based on status
        status = result.get('status', '')
        if 'Success' in status:
            color = QColor(200, 255, 200)  # Light green for success
        else:
            color = QColor(255, 200, 200)  # Light red for failure

        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(color)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(f"Check completed. Found {self.results_table.rowCount()} valid credentials.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)