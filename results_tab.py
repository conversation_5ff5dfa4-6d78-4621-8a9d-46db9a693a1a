import sys
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QCheckBox, QSpinBox, QFileDialog, QProgressBar,
                            QGroupBox, QMessageBox, QTabWidget, QComboBox)
from PyQt5.QtGui import QColor
from PyQt5.QtCore import Qt

class ResultsTab(QWidget):
    """Tab for Results Management functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Results tabs for different types
        self.results_tabs = QTabWidget()

        # MAC results tab
        self.mac_results_tab = QWidget()
        mac_layout = QVBoxLayout(self.mac_results_tab)

        # MAC results table
        self.mac_results_table = QTableWidget(0, 5)
        self.mac_results_table.setHorizontalHeaderLabels(["MAC", "Portal", "Status", "Created", "Expires"])
        self.mac_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        mac_layout.addWidget(self.mac_results_table)

        # User-Pass results tab
        self.user_pass_results_tab = QWidget()
        user_pass_layout = QVBoxLayout(self.user_pass_results_tab)

        # User-Pass results table
        self.user_pass_results_table = QTableWidget(0, 7)
        self.user_pass_results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status", "Created", "Expires", "Max Conn"])
        self.user_pass_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        user_pass_layout.addWidget(self.user_pass_results_table)

        # M3U results tab
        self.m3u_results_tab = QWidget()
        m3u_layout = QVBoxLayout(self.m3u_results_tab)

        # M3U results table
        self.m3u_results_table = QTableWidget(0, 5)
        self.m3u_results_table.setHorizontalHeaderLabels(["URL", "Status", "Channels", "Categories", "Expires"])
        self.m3u_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        m3u_layout.addWidget(self.m3u_results_table)

        # Xtream results tab
        self.xtream_results_tab = QWidget()
        xtream_layout = QVBoxLayout(self.xtream_results_tab)

        # Xtream results table
        self.xtream_results_table = QTableWidget(0, 7)
        self.xtream_results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status", "Expires", "Max Conn", "Categories"])
        self.xtream_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        xtream_layout.addWidget(self.xtream_results_table)

        # XUI Panel results tab
        self.xui_results_tab = QWidget()
        xui_layout = QVBoxLayout(self.xui_results_tab)

        # XUI Panel results table
        self.xui_results_table = QTableWidget(0, 6)
        self.xui_results_table.setHorizontalHeaderLabels(["Portal", "Username", "Password", "Status", "Total Users", "Active Users"])
        self.xui_results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        xui_layout.addWidget(self.xui_results_table)

        # Add tabs to results tabs widget
        self.results_tabs.addTab(self.mac_results_tab, "MAC Results")
        self.results_tabs.addTab(self.user_pass_results_tab, "User-Pass Results")
        self.results_tabs.addTab(self.m3u_results_tab, "M3U Results")
        self.results_tabs.addTab(self.xtream_results_tab, "Xtream Results")
        self.results_tabs.addTab(self.xui_results_tab, "XUI Panel Results")

        layout.addWidget(self.results_tabs)

        # Action buttons
        action_layout = QHBoxLayout()

        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_results)
        action_layout.addWidget(self.refresh_button)

        # Export options
        export_layout = QHBoxLayout()
        export_layout.addWidget(QLabel("Export Format:"))
        self.export_format = QComboBox()
        self.export_format.addItems(["CSV", "XLS", "TXT"])
        export_layout.addWidget(self.export_format)

        self.export_button = QPushButton("Export All")
        self.export_button.clicked.connect(self.export_results)
        export_layout.addWidget(self.export_button)

        action_layout.addLayout(export_layout)

        # Clear button
        self.clear_button = QPushButton("Clear List")
        self.clear_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_button)

        layout.addLayout(action_layout)

        # Last update info
        self.last_update_label = QLabel("Last update on: 01/06/2024 01:59:54")
        layout.addWidget(self.last_update_label)

        self.setLayout(layout)

    def refresh_results(self):
        """Refresh results from saved files"""
        self.load_mac_results()
        self.load_user_pass_results()
        self.load_m3u_results()
        self.load_xtream_results()
        self.load_xui_results()

        # Update last update time
        from datetime import datetime
        now = datetime.now()
        self.last_update_label.setText(f"Last update on: {now.strftime('%d/%m/%Y %H:%M:%S')}")

    def load_mac_results(self):
        """Load MAC results from file"""
        try:
            self.mac_results_table.setRowCount(0)

            # Try to load from good_mac.txt if exists
            try:
                with open('good_mac.txt', 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            parts = line.split('|')
                            row = self.mac_results_table.rowCount()
                            self.mac_results_table.insertRow(row)

                            # Add data to table
                            if len(parts) >= 1:
                                self.mac_results_table.setItem(row, 0, QTableWidgetItem(parts[0]))  # MAC
                            if len(parts) >= 2:
                                self.mac_results_table.setItem(row, 1, QTableWidgetItem(parts[1]))  # Portal

                            # Set default values for other columns
                            self.mac_results_table.setItem(row, 2, QTableWidgetItem("Active"))  # Status

                            # Highlight row
                            for col in range(self.mac_results_table.columnCount()):
                                item = self.mac_results_table.item(row, col)
                                if item:
                                    item.setBackground(QColor(200, 255, 200))  # Light green
            except:
                pass
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to load MAC results: {str(e)}")

    def load_user_pass_results(self):
        """Load User-Pass results from file"""
        try:
            self.user_pass_results_table.setRowCount(0)

            # Try to load from good_results.txt if exists
            try:
                with open('good_results.txt', 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            # Parse URL to extract portal, username, password
                            import re
                            match = re.search(r'(https?://[^/]+).*username=([^&]+).*password=([^&]+)', line)
                            if match:
                                portal = match.group(1)
                                username = match.group(2)
                                password = match.group(3)

                                row = self.user_pass_results_table.rowCount()
                                self.user_pass_results_table.insertRow(row)

                                # Add data to table
                                self.user_pass_results_table.setItem(row, 0, QTableWidgetItem(portal))
                                self.user_pass_results_table.setItem(row, 1, QTableWidgetItem(username))
                                self.user_pass_results_table.setItem(row, 2, QTableWidgetItem(password))
                                self.user_pass_results_table.setItem(row, 3, QTableWidgetItem("Active"))

                                # Highlight row with green for active results
                                for col in range(self.user_pass_results_table.columnCount()):
                                    item = self.user_pass_results_table.item(row, col)
                                    if item:
                                        item.setBackground(QColor(200, 255, 200))  # Light green
            except:
                pass

            # Also load from ERROR.txt if exists to show error results
            try:
                with open('ERROR.txt', 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            # Parse line to extract portal, username, password, and error
                            parts = line.split(' | ')
                            if len(parts) >= 2:
                                url = parts[0]
                                error = parts[1]

                                # Extract portal, username, password from URL
                                import re
                                match = re.search(r'(https?://[^/]+).*username=([^&]+).*password=([^&]+)', url)
                                if match:
                                    portal = match.group(1)
                                    username = match.group(2)
                                    password = match.group(3)

                                    row = self.user_pass_results_table.rowCount()
                                    self.user_pass_results_table.insertRow(row)

                                    # Add data to table
                                    self.user_pass_results_table.setItem(row, 0, QTableWidgetItem(portal))
                                    self.user_pass_results_table.setItem(row, 1, QTableWidgetItem(username))
                                    self.user_pass_results_table.setItem(row, 2, QTableWidgetItem(password))
                                    self.user_pass_results_table.setItem(row, 3, QTableWidgetItem(error))

                                    # Highlight row with red for error results
                                    for col in range(self.user_pass_results_table.columnCount()):
                                        item = self.user_pass_results_table.item(row, col)
                                        if item:
                                            item.setBackground(QColor(255, 200, 200))  # Light red
            except:
                pass
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to load User-Pass results: {str(e)}")

    def load_m3u_results(self):
        """Load M3U results from file"""
        try:
            self.m3u_results_table.setRowCount(0)

            # Try to load from good_m3u.txt if exists
            try:
                with open('good_m3u.txt', 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            row = self.m3u_results_table.rowCount()
                            self.m3u_results_table.insertRow(row)

                            # Add data to table
                            self.m3u_results_table.setItem(row, 0, QTableWidgetItem(line))  # URL
                            self.m3u_results_table.setItem(row, 1, QTableWidgetItem("Active"))  # Status

                            # Highlight row
                            for col in range(self.m3u_results_table.columnCount()):
                                item = self.m3u_results_table.item(row, col)
                                if item:
                                    item.setBackground(QColor(200, 255, 200))  # Light green
            except:
                pass
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to load M3U results: {str(e)}")

    def load_xtream_results(self):
        """Load Xtream results from file"""
        try:
            self.xtream_results_table.setRowCount(0)

            # Try to load from good_xtream.txt if exists
            try:
                with open('good_xtream.txt', 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            # Parse URL to extract portal, username, password
                            import re
                            match = re.search(r'(https?://[^/]+).*username=([^&]+).*password=([^&]+)', line)
                            if match:
                                portal = match.group(1)
                                username = match.group(2)
                                password = match.group(3)

                                row = self.xtream_results_table.rowCount()
                                self.xtream_results_table.insertRow(row)

                                # Add data to table
                                self.xtream_results_table.setItem(row, 0, QTableWidgetItem(portal))
                                self.xtream_results_table.setItem(row, 1, QTableWidgetItem(username))
                                self.xtream_results_table.setItem(row, 2, QTableWidgetItem(password))
                                self.xtream_results_table.setItem(row, 3, QTableWidgetItem("Active"))

                                # Highlight row
                                for col in range(self.xtream_results_table.columnCount()):
                                    item = self.xtream_results_table.item(row, col)
                                    if item:
                                        item.setBackground(QColor(200, 255, 200))  # Light green
            except:
                pass
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to load Xtream results: {str(e)}")

    def load_xui_results(self):
        """Load XUI Panel results from file"""
        try:
            self.xui_results_table.setRowCount(0)

            # Try to load from good_xui_panels.txt if exists
            try:
                with open('good_xui_panels.txt', 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            # Parse line to extract portal, username, password
                            parts = line.split(' - ')
                            if len(parts) >= 2:
                                portal = parts[0]
                                creds = parts[1].split(':')
                                if len(creds) >= 2:
                                    username = creds[0]
                                    password = creds[1]

                                    row = self.xui_results_table.rowCount()
                                    self.xui_results_table.insertRow(row)

                                    # Add data to table
                                    self.xui_results_table.setItem(row, 0, QTableWidgetItem(portal))
                                    self.xui_results_table.setItem(row, 1, QTableWidgetItem(username))
                                    self.xui_results_table.setItem(row, 2, QTableWidgetItem(password))
                                    self.xui_results_table.setItem(row, 3, QTableWidgetItem("Active"))

                                    # Highlight row
                                    for col in range(self.xui_results_table.columnCount()):
                                        item = self.xui_results_table.item(row, col)
                                        if item:
                                            item.setBackground(QColor(200, 255, 200))  # Light green
            except:
                pass
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to load XUI Panel results: {str(e)}")

    def export_results(self):
        """Export results to selected format"""
        current_tab = self.results_tabs.currentIndex()
        tab_name = self.results_tabs.tabText(current_tab)

        # Get current table based on selected tab
        if current_tab == 0:
            table = self.mac_results_table
        elif current_tab == 1:
            table = self.user_pass_results_table
        elif current_tab == 2:
            table = self.m3u_results_table
        elif current_tab == 3:
            table = self.xtream_results_table
        elif current_tab == 4:
            table = self.xui_results_table
        else:
            QMessageBox.warning(self, "Warning", "Invalid tab selected.")
            return

        if table.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No results to export.")
            return

        export_format = self.export_format.currentText()

        if export_format == "CSV":
            self.export_to_csv(table, tab_name)
        elif export_format == "XLS":
            self.export_to_xls(table, tab_name)
        elif export_format == "TXT":
            self.export_to_txt(table, tab_name)

    def export_to_csv(self, table, tab_name):
        """Export table to CSV file"""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save CSV", f"{tab_name}.csv", "CSV Files (*.csv);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    # Write header
                    header = []
                    for col in range(table.columnCount()):
                        header.append(table.horizontalHeaderItem(col).text())
                    f.write(','.join(header) + '\n')

                    # Write data
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            if item:
                                # Escape commas in cell data
                                cell_data = item.text().replace(',', '\\,')
                                row_data.append(cell_data)
                            else:
                                row_data.append('')
                        f.write(','.join(row_data) + '\n')

                QMessageBox.information(self, "Success", f"Exported {table.rowCount()} rows to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export to CSV: {str(e)}")

    def export_to_xls(self, table, tab_name):
        """Export table to XLS file"""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save XLS", f"{tab_name}.xls", "XLS Files (*.xls);;All Files (*)")
        if file_path:
            try:
                # Try to use xlwt if available
                try:
                    import xlwt

                    workbook = xlwt.Workbook()
                    sheet = workbook.add_sheet(tab_name)

                    # Write header
                    for col in range(table.columnCount()):
                        sheet.write(0, col, table.horizontalHeaderItem(col).text())

                    # Write data
                    for row in range(table.rowCount()):
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            if item:
                                sheet.write(row + 1, col, item.text())

                    workbook.save(file_path)
                    QMessageBox.information(self, "Success", f"Exported {table.rowCount()} rows to {file_path}")
                except ImportError:
                    # Fallback to CSV if xlwt is not available
                    QMessageBox.warning(self, "Warning", "XLS export requires xlwt module. Exporting as CSV instead.")
                    self.export_to_csv(table, tab_name)
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export to XLS: {str(e)}")

    def export_to_txt(self, table, tab_name):
        """Export table to TXT file"""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save TXT", f"{tab_name}.txt", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    # Write header
                    header = []
                    for col in range(table.columnCount()):
                        header.append(table.horizontalHeaderItem(col).text())
                    f.write('\t'.join(header) + '\n')

                    # Write data
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            if item:
                                row_data.append(item.text())
                            else:
                                row_data.append('')
                        f.write('\t'.join(row_data) + '\n')

                QMessageBox.information(self, "Success", f"Exported {table.rowCount()} rows to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export to TXT: {str(e)}")

    def clear_results(self):
        """Clear current results table"""
        current_tab = self.results_tabs.currentIndex()

        # Get current table based on selected tab
        if current_tab == 0:
            table = self.mac_results_table
        elif current_tab == 1:
            table = self.user_pass_results_table
        elif current_tab == 2:
            table = self.m3u_results_table
        elif current_tab == 3:
            table = self.xtream_results_table
        elif current_tab == 4:
            table = self.xui_results_table
        else:
            QMessageBox.warning(self, "Warning", "Invalid tab selected.")
            return

        reply = QMessageBox.question(self, "Confirm", "Are you sure you want to clear all results in this tab?",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            table.setRowCount(0)
