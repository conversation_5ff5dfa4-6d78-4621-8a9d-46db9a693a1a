import sys
import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QAction, QMessageBox
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt

# Add parent directory to path to make imports work when running from src directory
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Use imports with fallbacks for PyInstaller compatibility
try:
    # Try direct imports first (for PyInstaller)
    from m3u_tab import M3UTab
    from xtream_code_tab import XtreamCodeTab
    from xui_panel_tab import XUIPanelTab
    from portal_extractor_tab import PortalExtractorTab
    from link_extractor_tab import LinkExtractorTab
    from about_tab import AboutTab
    from config import get_config, set_config, load_config
except ImportError:
    # Fall back to package imports (for development)
    from src.m3u_tab import M3UTab
    from src.xtream_code_tab import Xtream<PERSON>ode<PERSON>ab
    from src.xui_panel_tab import XUIPanelTab
    from src.portal_extractor_tab import PortalExtractorTab
    from src.link_extractor_tab import LinkExtractorTab
    from src.about_tab import AboutTab
    from src.config import get_config, set_config, load_config

# Get logger
logger = logging.getLogger("iptv_checker.app")

class IPTVTools(QMainWindow):
    def __init__(self):
        super().__init__()

        # Load configuration
        config = load_config()
        logger.info("Application starting")

        # Set window properties
        self.setWindowTitle("Twins-Cracking IPTV By PS-DRAGON V1.0.0")
        self.setGeometry(100, 100, 1200, 800)

        # Main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # Create tab widget
        self.tabs = QTabWidget()
        self.main_layout.addWidget(self.tabs)

        # Create tabs
        try:
            # Try direct import first (for PyInstaller)
            from main import MACTab, UserPassTab
        except ImportError:
            # Fall back to package import (for development)
            from src.main import MACTab, UserPassTab
        self.mac_scanner_tab = MACTab()
        self.user_pass_tab = UserPassTab()
        self.m3u_checker_tab = M3UTab()
        # MY RESULTS tab removed as requested
        self.xtream_code_tab = XtreamCodeTab()
        self.xui_panel_tab = XUIPanelTab()
        self.portal_extractor_tab = PortalExtractorTab()
        self.link_extractor_tab = LinkExtractorTab()
        self.about_tab = AboutTab()

        # Add tabs to widget
        self.tabs.addTab(self.mac_scanner_tab, "MAC SCANNER")
        self.tabs.addTab(self.user_pass_tab, "USER-PASS")
        self.tabs.addTab(self.m3u_checker_tab, "M3U CHECKER")
        # MY RESULTS tab removed as requested
        self.tabs.addTab(self.xtream_code_tab, "XTREAM CODE")
        self.tabs.addTab(self.xui_panel_tab, "XUI PANEL")
        self.tabs.addTab(self.portal_extractor_tab, "PORTAL EXTRACTOR")
        self.tabs.addTab(self.link_extractor_tab, "LINK EXTRACTOR")
        self.tabs.addTab(self.about_tab, "ABOUT & ACTIVATION")

        # Initialize UI components
        self.init_ui()

        logger.info("Application UI initialized")

    def init_ui(self):
        # Status bar
        self.statusBar().showMessage("Ready")

        # Menu bar
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        settings_action = QAction('Settings', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def show_settings(self):
        """Show settings dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Settings", "Settings dialog will be implemented in a future version.")

    def show_about(self):
        """Show about dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.about(self, "About", "Twins-Cracking IPTV By PS-DRAGON V1.0.0\n\nA comprehensive tool for IPTV management and testing.")

def main():
    # Configure logging
    import logging.config
    logging_config = {
        'version': 1,
        'formatters': {
            'standard': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': 'INFO',
                'formatter': 'standard',
                'stream': 'ext://sys.stdout'
            },
            'file': {
                'class': 'logging.FileHandler',
                'level': 'DEBUG',
                'formatter': 'standard',
                'filename': 'iptv_checker.log',
                'mode': 'a',
            }
        },
        'loggers': {
            'iptv_checker': {
                'handlers': ['console', 'file'],
                'level': 'DEBUG',
                'propagate': False
            }
        }
    }
    logging.config.dictConfig(logging_config)
    logger = logging.getLogger("iptv_checker")

    # Disable SSL warnings
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    logger.info("SSL warnings disabled")

    # Install PySocks if not already installed
    try:
        import pip
        try:
            import socks
            logger.debug("PySocks already installed")
        except ImportError:
            logger.info("Installing PySocks package")
            pip.main(['install', 'PySocks'])
    except Exception as e:
        logger.error(f"Error with PySocks: {str(e)}")

    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
        logger.info("Created logs directory")

    # Create ERROR.txt file if it doesn't exist
    if not os.path.exists('ERROR.txt'):
        with open('ERROR.txt', 'w', encoding='utf-8') as f:
            f.write("# IPTV Checker Error Log\n")
            f.write("# Format: URL | Error Type\n")
            f.write(f"# Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        logger.info("Created ERROR.txt file")

    # Start application
    logger.info("Starting application")
    app = QApplication(sys.argv)
    window = IPTVTools()
    window.show()

    # Exit when app is closed
    exit_code = app.exec_()
    logger.info(f"Application exiting with code {exit_code}")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
