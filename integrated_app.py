"""
Integrated IPTV Tools Application
This is the main application file that integrates all tools into a single application
with a modern, user-friendly interface.
"""

import sys
import os
import logging
import logging.config
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                            QCheckBox, QSpinBox, QFileDialog, QProgressBar, QGroupBox,
                            QRadioButton, QMessageBox, QMenu, QAction, QSplashScreen,
                            QStatusBar, QFrame, QSizePolicy)
from PyQt5.QtGui import QIcon, QColor, QFont, QPixmap, QPalette, QCursor
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize

# Import all modules
from mac_scanner import MACScanner
from user_pass_checker import User<PERSON><PERSON><PERSON><PERSON><PERSON>
from m3u_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from xtream_code_checker import XtreamCodeChecker
from xui_panel_checker import XUIPanelChecker
from portal_extractor import PortalExtractor
from link_extractor import LinkExtractor
from subdomain_finder import SubdomainFinder
from utils import RequestsSession, format_proxy, save_error_to_file, send_to_telegram
from config import get_config, set_config, load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("iptv_tools.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("iptv_tools")

class TelegramSettingsWidget(QWidget):
    """Widget for Telegram settings"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Telegram group
        telegram_group = QGroupBox("Telegram Settings")
        telegram_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #3498db;
            }
        """)
        telegram_layout = QVBoxLayout()

        # Enable Telegram
        self.telegram_enabled = QCheckBox("Enable Telegram Notifications")
        self.telegram_enabled.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 10pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        telegram_layout.addWidget(self.telegram_enabled)

        # Option to send errors to Telegram
        self.send_errors = QCheckBox("Send Error Results to Telegram")
        self.send_errors.setChecked(False)  # Disabled by default
        self.send_errors.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 10pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        telegram_layout.addWidget(self.send_errors)

        # Bot token
        token_layout = QHBoxLayout()
        token_label = QLabel("Bot Token:")
        token_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        token_layout.addWidget(token_label)
        self.bot_token = QLineEdit()
        self.bot_token.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        token_layout.addWidget(self.bot_token)
        telegram_layout.addLayout(token_layout)

        # Chat ID
        chat_id_layout = QHBoxLayout()
        chat_id_label = QLabel("Chat ID:")
        chat_id_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        chat_id_layout.addWidget(chat_id_label)
        self.chat_id = QLineEdit()
        self.chat_id.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        chat_id_layout.addWidget(self.chat_id)
        telegram_layout.addLayout(chat_id_layout)

        # Test button
        self.test_button = QPushButton("Test Telegram Bot")
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.test_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.test_button.clicked.connect(self.test_telegram)
        telegram_layout.addWidget(self.test_button)

        telegram_group.setLayout(telegram_layout)
        layout.addWidget(telegram_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        self.setLayout(layout)

    def test_telegram(self):
        """Test Telegram bot connection"""
        import requests

        if not self.telegram_enabled.isChecked():
            QMessageBox.warning(self, "Warning", "Telegram notifications are not enabled.")
            return

        token = self.bot_token.text().strip()
        chat_id = self.chat_id.text().strip()

        if not token or not chat_id:
            QMessageBox.warning(self, "Warning", "Please enter both Bot Token and Chat ID.")
            return

        try:
            message = "🧪 Test message from IPTV Tools"
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            payload = {
                "chat_id": chat_id,
                "text": message
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                QMessageBox.information(self, "Success", "Test message sent successfully!")
            else:
                data = response.json()
                QMessageBox.critical(self, "Error", f"Failed to send message: {data.get('description', 'Unknown error')}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to send message: {str(e)}")

class MACTab(QWidget):
    """Tab for MAC Scanner functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.scanner = MACScanner()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #3498db;
            }
        """)
        config_layout = QVBoxLayout()

        # Portal input
        portal_layout = QHBoxLayout()
        portal_label = QLabel("Portal:")
        portal_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        portal_layout.addWidget(portal_label)
        self.portal_input = QLineEdit()
        self.portal_input.setPlaceholderText("http://example.com:8080")
        self.portal_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        portal_layout.addWidget(self.portal_input)
        config_layout.addLayout(portal_layout)

        # MAC input
        mac_layout = QHBoxLayout()
        mac_label = QLabel("MAC:")
        mac_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        mac_layout.addWidget(mac_label)
        self.mac_input = QTextEdit()
        self.mac_input.setPlaceholderText("Enter MAC addresses, one per line")
        self.mac_input.setMaximumHeight(100)
        self.mac_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        mac_layout.addWidget(self.mac_input)
        config_layout.addLayout(mac_layout)

        # Buttons for loading MACs
        buttons_layout = QHBoxLayout()
        self.load_mac_button = QPushButton("Load MAC List")
        self.load_mac_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.load_mac_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.load_mac_button.clicked.connect(self.load_mac_list)
        buttons_layout.addWidget(self.load_mac_button)

        self.clear_mac_button = QPushButton("Clear")
        self.clear_mac_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        self.clear_mac_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_mac_button.clicked.connect(self.clear_mac_list)
        buttons_layout.addWidget(self.clear_mac_button)

        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.remove_duplicates_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        buttons_layout.addWidget(self.remove_duplicates_button)
        config_layout.addLayout(buttons_layout)

        # Proxy settings
        proxy_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        self.use_proxy.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 10pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        proxy_layout.addWidget(self.use_proxy)

        self.load_proxy_button = QPushButton("Load Proxy List")
        self.load_proxy_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:pressed {
                background-color: #6c3483;
            }
        """)
        self.load_proxy_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.load_proxy_button.clicked.connect(self.load_proxy_list)
        proxy_layout.addWidget(self.load_proxy_button)

        self.proxy_count_label = QLabel("Proxies: 0")
        self.proxy_count_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        proxy_layout.addWidget(self.proxy_count_label)

        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_label = QLabel("Max Threads:")
        thread_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        thread_layout.addWidget(thread_label)
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        self.thread_count.setStyleSheet("""
            QSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QSpinBox:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        thread_layout.addWidget(self.thread_count)

        timeout_label = QLabel("Timeout (sec):")
        timeout_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        thread_layout.addWidget(timeout_label)
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        self.timeout.setStyleSheet("""
            QSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QSpinBox:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # MAC Generator group
        mac_gen_group = QGroupBox("MAC Address Generator")
        mac_gen_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #f39c12;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #f39c12;
            }
        """)
        mac_gen_layout = QVBoxLayout()

        # Prefix input
        prefix_layout = QHBoxLayout()
        prefix_label = QLabel("Fixed Prefix:")
        prefix_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        prefix_layout.addWidget(prefix_label)
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("e.g., 00:1A:79:")
        self.prefix_input.setText("00:1A:79:")
        self.prefix_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        prefix_layout.addWidget(self.prefix_input)
        mac_gen_layout.addLayout(prefix_layout)

        # Count input
        count_layout = QHBoxLayout()
        count_label = QLabel("Number of MACs:")
        count_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        count_layout.addWidget(count_label)
        self.mac_count = QSpinBox()
        self.mac_count.setMinimum(1)
        self.mac_count.setMaximum(100000)
        self.mac_count.setValue(10000)
        self.mac_count.setStyleSheet("""
            QSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QSpinBox:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        count_layout.addWidget(self.mac_count)

        # Generate button
        self.generate_mac_button = QPushButton("Generate MAC Addresses")
        self.generate_mac_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
            QPushButton:pressed {
                background-color: #a04000;
            }
        """)
        self.generate_mac_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.generate_mac_button.clicked.connect(self.generate_mac_addresses)
        count_layout.addWidget(self.generate_mac_button)

        mac_gen_layout.addLayout(count_layout)
        mac_gen_group.setLayout(mac_gen_layout)
        layout.addWidget(mac_gen_group)

        # Telegram settings
        self.telegram_settings = TelegramSettingsWidget()
        layout.addWidget(self.telegram_settings)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Scan")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.start_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.start_button.clicked.connect(self.start_scan)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Scan")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.stop_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.stop_button.clicked.connect(self.stop_scan)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.setStyleSheet("""
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
            QPushButton:pressed {
                background-color: #1a252f;
            }
        """)
        self.clear_results_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                text-align: center;
                height: 20px;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                width: 10px;
                margin: 0.5px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 10pt;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["MAC", "Portal", "Status", "Created", "Expires"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from scanner to UI"""
        self.scanner.result_signal.connect(self.add_result)
        self.scanner.progress_signal.connect(self.update_progress)
        self.scanner.status_signal.connect(self.update_status)

        # Update scanner settings when UI changes
        self.thread_count.valueChanged.connect(self.update_scanner_settings)
        self.timeout.valueChanged.connect(self.update_scanner_settings)
        self.telegram_settings.telegram_enabled.toggled.connect(self.update_scanner_settings)
        self.telegram_settings.send_errors.toggled.connect(self.update_scanner_settings)
        self.telegram_settings.bot_token.textChanged.connect(self.update_scanner_settings)
        self.telegram_settings.chat_id.textChanged.connect(self.update_scanner_settings)

    def update_scanner_settings(self):
        """Update scanner settings from UI"""
        self.scanner.max_threads = self.thread_count.value()
        self.scanner.timeout = self.timeout.value()
        self.scanner.telegram_enabled = self.telegram_settings.telegram_enabled.isChecked()
        self.scanner.telegram_bot_token = self.telegram_settings.bot_token.text().strip()
        self.scanner.telegram_chat_id = self.telegram_settings.chat_id.text().strip()
        self.scanner.send_errors_to_telegram = self.telegram_settings.send_errors.isChecked()

    def load_mac_list(self):
        """Load MAC list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open MAC List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    macs = f.read()
                    self.mac_input.setText(macs)
                    self.status_label.setText(f"Loaded {len(macs.splitlines())} MACs from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load MAC list: {str(e)}")

    def clear_mac_list(self):
        """Clear MAC list"""
        self.mac_input.clear()

    def remove_duplicates(self):
        """Remove duplicate MAC addresses from the list"""
        mac_text = self.mac_input.toPlainText().strip()
        if not mac_text:
            return

        # Get unique MAC addresses while preserving order
        mac_list = [line.strip() for line in mac_text.splitlines() if line.strip()]
        unique_macs = []
        seen = set()
        for mac in mac_list:
            if mac.lower() not in seen:
                seen.add(mac.lower())
                unique_macs.append(mac)

        # Update the text area with unique MACs
        self.mac_input.setText("\n".join(unique_macs))

        # Show message about removed duplicates
        removed_count = len(mac_list) - len(unique_macs)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate MAC addresses")
        else:
            self.status_label.setText("No duplicate MAC addresses found")

    def load_proxy_list(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                count = self.scanner.load_proxies(file_path)
                self.proxy_count_label.setText(f"Proxies: {count}")
                self.status_label.setText(f"Loaded {count} proxies from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load proxy list: {str(e)}")

    def generate_mac_addresses(self):
        """Generate MAC addresses with the given prefix"""
        import random

        prefix = self.prefix_input.text().strip()
        count = self.mac_count.value()

        # Validate prefix
        if not prefix:
            QMessageBox.warning(self, "Warning", "Please enter a MAC prefix.")
            return

        # Generate MAC addresses
        try:
            macs = []
            prefix_parts = prefix.split(':')

            # Determine how many parts are fixed and how many need to be generated
            fixed_parts = len(prefix_parts)
            if prefix_parts[-1] == '':  # Handle trailing colon
                fixed_parts -= 1

            if fixed_parts > 6:
                QMessageBox.warning(self, "Warning", "MAC prefix is too long. A MAC address has 6 parts.")
                return

            # Generate the specified number of MAC addresses
            for _ in range(count):
                mac_parts = prefix_parts.copy()

                # Generate random parts to complete the MAC address
                for i in range(fixed_parts, 6):
                    mac_parts.append(f"{random.randint(0, 255):02x}")

                # Join parts and add to list
                mac = ':'.join(mac_parts[:6])  # Ensure exactly 6 parts
                macs.append(mac)

            # Update the text area with generated MACs
            self.mac_input.setText("\n".join(macs))
            self.status_label.setText(f"Generated {count} MAC addresses with prefix {prefix}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate MAC addresses: {str(e)}")

    def start_scan(self):
        """Start MAC scanning"""
        portal = self.portal_input.text().strip()
        if not portal:
            QMessageBox.warning(self, "Warning", "Please enter a portal URL.")
            return

        # Add http:// if missing
        if not portal.startswith('http://') and not portal.startswith('https://'):
            portal = f"http://{portal}"
            self.portal_input.setText(portal)

        # Get MAC list
        mac_text = self.mac_input.toPlainText().strip()
        if not mac_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one MAC address.")
            return

        mac_list = [line.strip() for line in mac_text.splitlines() if line.strip()]

        # Update scanner settings
        self.update_scanner_settings()

        # Start scanning
        success = self.scanner.start_scan(mac_list, portal, self.use_proxy.isChecked())

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Scanning...")
            self.progress_bar.setMaximum(len(mac_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start scanning.")

    def stop_scan(self):
        """Stop MAC scanning"""
        self.scanner.stop_scan()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Scan stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        status = result.get('status', '')

        # Add the result to the table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('mac', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('portal', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('created', '')))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('expires', '')))

        # Highlight row based on status
        if 'Active' in status:
            color = QColor(200, 255, 200)  # Light green for active
        elif 'Unauthorized' in status:
            color = QColor(255, 200, 200)  # Light red for unauthorized
        else:
            color = QColor(255, 255, 200)  # Light yellow for other statuses

        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(color)

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(f"Scan completed. Found {self.results_table.rowCount()} active MACs.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

class M3UTab(QWidget):
    """Tab for M3U Checker functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checker = M3UChecker()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #3498db;
            }
        """)
        config_layout = QVBoxLayout()

        # M3U URLs input
        urls_layout = QHBoxLayout()
        urls_label = QLabel("M3U URLs:")
        urls_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        urls_layout.addWidget(urls_label)
        self.urls_input = QTextEdit()
        self.urls_input.setPlaceholderText("Enter M3U URLs, one per line")
        self.urls_input.setMaximumHeight(100)
        self.urls_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        urls_layout.addWidget(self.urls_input)
        config_layout.addLayout(urls_layout)

        # Buttons for loading URLs
        buttons_layout = QHBoxLayout()
        self.load_urls_button = QPushButton("Load URLs")
        self.load_urls_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.load_urls_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.load_urls_button.clicked.connect(self.load_urls)
        buttons_layout.addWidget(self.load_urls_button)

        self.clear_urls_button = QPushButton("Clear")
        self.clear_urls_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        self.clear_urls_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_urls_button.clicked.connect(self.clear_urls)
        buttons_layout.addWidget(self.clear_urls_button)

        self.remove_duplicates_button = QPushButton("Remove Duplicates")
        self.remove_duplicates_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.remove_duplicates_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.remove_duplicates_button.clicked.connect(self.remove_duplicates)
        buttons_layout.addWidget(self.remove_duplicates_button)
        config_layout.addLayout(buttons_layout)

        # Proxy settings
        proxy_layout = QHBoxLayout()
        self.use_proxy = QCheckBox("Use Proxy")
        self.use_proxy.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 10pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        proxy_layout.addWidget(self.use_proxy)

        self.load_proxy_button = QPushButton("Load Proxy List")
        self.load_proxy_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:pressed {
                background-color: #6c3483;
            }
        """)
        self.load_proxy_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.load_proxy_button.clicked.connect(self.load_proxy_list)
        proxy_layout.addWidget(self.load_proxy_button)

        self.proxy_count_label = QLabel("Proxies: 0")
        self.proxy_count_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        proxy_layout.addWidget(self.proxy_count_label)

        proxy_layout.addStretch()
        config_layout.addLayout(proxy_layout)

        # Thread settings
        thread_layout = QHBoxLayout()
        thread_label = QLabel("Max Threads:")
        thread_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        thread_layout.addWidget(thread_label)
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(100)
        self.thread_count.setValue(10)
        self.thread_count.setStyleSheet("""
            QSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QSpinBox:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        thread_layout.addWidget(self.thread_count)

        timeout_label = QLabel("Timeout (sec):")
        timeout_label.setStyleSheet("color: #2c3e50; font-size: 10pt;")
        thread_layout.addWidget(timeout_label)
        self.timeout = QSpinBox()
        self.timeout.setMinimum(1)
        self.timeout.setMaximum(30)
        self.timeout.setValue(5)
        self.timeout.setStyleSheet("""
            QSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                color: #2c3e50;
            }
            QSpinBox:focus {
                border: 1px solid #3498db;
                background-color: white;
            }
        """)
        thread_layout.addWidget(self.timeout)

        thread_layout.addStretch()
        config_layout.addLayout(thread_layout)

        # Auto save option
        self.auto_save = QCheckBox("Auto-save good results to file")
        self.auto_save.setChecked(True)
        self.auto_save.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 10pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        config_layout.addWidget(self.auto_save)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Telegram settings
        self.telegram_settings = TelegramSettingsWidget()
        layout.addWidget(self.telegram_settings)

        # Action buttons
        action_layout = QHBoxLayout()
        self.start_button = QPushButton("Start Check")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.start_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.start_button.clicked.connect(self.start_check)
        action_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("Stop Check")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.stop_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.stop_button.clicked.connect(self.stop_check)
        self.stop_button.setEnabled(False)
        action_layout.addWidget(self.stop_button)

        self.clear_results_button = QPushButton("Clear Results")
        self.clear_results_button.setStyleSheet("""
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
            QPushButton:pressed {
                background-color: #1a252f;
            }
        """)
        self.clear_results_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_results_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_results_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                text-align: center;
                height: 20px;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                width: 10px;
                margin: 0.5px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 10pt;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(["URL", "Status", "Channels", "Categories", "Expires"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from checker to UI"""
        self.checker.result_signal.connect(self.add_result)
        self.checker.progress_signal.connect(self.update_progress)
        self.checker.status_signal.connect(self.update_status)

        # Update checker settings when UI changes
        self.thread_count.valueChanged.connect(self.update_checker_settings)
        self.timeout.valueChanged.connect(self.update_checker_settings)
        self.auto_save.toggled.connect(self.update_checker_settings)
        self.telegram_settings.telegram_enabled.toggled.connect(self.update_checker_settings)
        self.telegram_settings.bot_token.textChanged.connect(self.update_checker_settings)
        self.telegram_settings.chat_id.textChanged.connect(self.update_checker_settings)

    def update_checker_settings(self):
        """Update checker settings from UI"""
        self.checker.max_threads = self.thread_count.value()
        self.checker.timeout = self.timeout.value()
        self.checker.auto_save = self.auto_save.isChecked()
        self.checker.telegram_enabled = self.telegram_settings.telegram_enabled.isChecked()
        self.checker.telegram_bot_token = self.telegram_settings.bot_token.text().strip()
        self.checker.telegram_chat_id = self.telegram_settings.chat_id.text().strip()

    def load_urls(self):
        """Load M3U URLs from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open M3U URLs List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    urls = f.read()
                    self.urls_input.setText(urls)
                    self.status_label.setText(f"Loaded {len(urls.splitlines())} URLs from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load URLs: {str(e)}")

    def clear_urls(self):
        """Clear M3U URLs"""
        self.urls_input.clear()

    def remove_duplicates(self):
        """Remove duplicate URLs from the list"""
        urls_text = self.urls_input.toPlainText().strip()
        if not urls_text:
            return

        # Get unique URLs while preserving order
        urls_list = [line.strip() for line in urls_text.splitlines() if line.strip()]
        unique_urls = []
        seen = set()
        for url in urls_list:
            if url.lower() not in seen:
                seen.add(url.lower())
                unique_urls.append(url)

        # Update the text area with unique URLs
        self.urls_input.setText("\n".join(unique_urls))

        # Show message about removed duplicates
        removed_count = len(urls_list) - len(unique_urls)
        if removed_count > 0:
            self.status_label.setText(f"Removed {removed_count} duplicate URLs")
        else:
            self.status_label.setText("No duplicate URLs found")

    def load_proxy_list(self):
        """Load proxy list from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Proxy List", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                count = self.checker.load_proxies(file_path)
                self.proxy_count_label.setText(f"Proxies: {count}")
                self.status_label.setText(f"Loaded {count} proxies from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load proxy list: {str(e)}")

    def test_telegram(self):
        """Test Telegram bot connection"""
        import requests

        if not self.telegram_settings.telegram_enabled.isChecked():
            QMessageBox.warning(self, "Warning", "Telegram notifications are not enabled.")
            return

        token = self.telegram_settings.bot_token.text().strip()
        chat_id = self.telegram_settings.chat_id.text().strip()

        if not token or not chat_id:
            QMessageBox.warning(self, "Warning", "Please enter both Bot Token and Chat ID.")
            return

        try:
            message = "🧪 Test message from IPTV Tools"
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            payload = {
                "chat_id": chat_id,
                "text": message
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                QMessageBox.information(self, "Success", "Test message sent successfully!")
            else:
                data = response.json()
                QMessageBox.critical(self, "Error", f"Failed to send message: {data.get('description', 'Unknown error')}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to send message: {str(e)}")

    def start_check(self):
        """Start M3U checking"""
        # Get M3U URLs list
        urls_text = self.urls_input.toPlainText().strip()
        if not urls_text:
            QMessageBox.warning(self, "Warning", "Please enter at least one M3U URL.")
            return

        urls_list = [line.strip() for line in urls_text.splitlines() if line.strip()]

        # Update checker settings
        self.update_checker_settings()

        # Start checking
        success = self.checker.start_check(urls_list, self.use_proxy.isChecked())

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Checking...")
            self.progress_bar.setMaximum(len(urls_list))
            self.progress_bar.setValue(0)
        else:
            QMessageBox.critical(self, "Error", "Failed to start checking.")

    def stop_check(self):
        """Stop M3U checking"""
        self.checker.stop_check()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Check stopped")

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)

    def add_result(self, result):
        """Add result to table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('url', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(result.get('status', '')))
        self.results_table.setItem(row, 2, QTableWidgetItem(str(result.get('channels', ''))))

        # Format categories
        categories = result.get('categories', [])
        if categories:
            categories_str = ', '.join(categories[:5])
            if len(categories) > 5:
                categories_str += f" and {len(categories) - 5} more"
        else:
            categories_str = ""

        self.results_table.setItem(row, 3, QTableWidgetItem(categories_str))
        self.results_table.setItem(row, 4, QTableWidgetItem(result.get('expires', '')))

        # Highlight row
        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(QColor(200, 255, 200))  # Light green

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setValue(current)
        if current >= total:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(f"Check completed. Found {self.results_table.rowCount()} valid M3U playlists.")

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)

class AboutTab(QWidget):
    """Tab for About and Activation information"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Logo and title
        title_layout = QHBoxLayout()

        # Add logo placeholder
        logo_label = QLabel()
        logo_label.setFixedSize(100, 100)
        logo_label.setStyleSheet("background-color: #3498db; border-radius: 50px;")
        title_layout.addWidget(logo_label)

        # Title and version
        title_info = QVBoxLayout()
        title_label = QLabel("IPTV Tools")
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #2c3e50;")
        title_info.addWidget(title_label)

        version_label = QLabel("Version 1.0.0")
        version_label.setStyleSheet("font-size: 12pt; color: #7f8c8d;")
        title_info.addWidget(version_label)

        title_layout.addLayout(title_info)
        title_layout.addStretch()

        layout.addLayout(title_layout)

        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #bdc3c7;")
        layout.addWidget(separator)

        # Description
        description = QLabel(
            "IPTV Tools is a comprehensive suite for managing and testing IPTV services. "
            "It includes tools for MAC scanning, M3U checking, Xtream Code testing, "
            "XUI panel checking, portal extraction, and more."
        )
        description.setWordWrap(True)
        description.setStyleSheet("font-size: 11pt; color: #2c3e50; margin: 10px 0;")
        layout.addWidget(description)

        # Features
        features_group = QGroupBox("Features")
        features_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #3498db;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #3498db;
            }
        """)

        features_layout = QVBoxLayout()
        features = [
            "MAC Scanner - Test MAC addresses against portals",
            "M3U Checker - Validate M3U playlists and extract channel information",
            "Xtream Code Checker - Test Xtream Code credentials",
            "XUI Panel Checker - Test XUI panel credentials",
            "Portal Extractor - Extract portal information",
            "Link Extractor - Extract links from websites",
            "Subdomain Finder - Find subdomains for a domain"
        ]

        for feature in features:
            feature_label = QLabel("• " + feature)
            feature_label.setStyleSheet("font-size: 10pt; color: #2c3e50; margin: 2px 0;")
            features_layout.addWidget(feature_label)

        features_group.setLayout(features_layout)
        layout.addWidget(features_group)

        # Contact information
        contact_group = QGroupBox("Contact Information")
        contact_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #e74c3c;
            }
        """)

        contact_layout = QVBoxLayout()
        contact_info = QLabel(
            "For support or inquiries, please contact us:\n"
            "Email: <EMAIL>\n"
            "Website: https://iptvtools.com\n"
            "Telegram: @iptvtools"
        )
        contact_info.setStyleSheet("font-size: 10pt; color: #2c3e50;")
        contact_layout.addWidget(contact_info)

        contact_group.setLayout(contact_layout)
        layout.addWidget(contact_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        self.setLayout(layout)

class IPTVTools(QMainWindow):
    def __init__(self):
        super().__init__()

        # Load configuration
        config = load_config()
        logger.info("Application starting")

        # Set window properties
        self.setWindowTitle("IPTV Tools")
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
        """)

        # Main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # Create tab widget
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 12px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #d6dbdf;
            }
        """)
        self.main_layout.addWidget(self.tabs)

        # Create tabs
        self.mac_scanner_tab = MACTab()
        self.m3u_checker_tab = M3UTab()
        self.about_tab = AboutTab()

        # Add tabs to widget
        self.tabs.addTab(self.mac_scanner_tab, "MAC SCANNER")
        self.tabs.addTab(self.m3u_checker_tab, "M3U CHECKER")
        self.tabs.addTab(self.about_tab, "ABOUT")

        # Initialize UI components
        self.init_ui()

        logger.info("Application UI initialized")

    def init_ui(self):
        # Status bar
        self.statusBar().showMessage("Ready")
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
        """)

        # Menu bar
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #2c3e50;
                color: white;
            }
            QMenuBar::item {
                background-color: transparent;
                color: white;
                padding: 5px 10px;
            }
            QMenuBar::item:selected {
                background-color: #3498db;
            }
            QMenu {
                background-color: #2c3e50;
                color: white;
                border: 1px solid #34495e;
            }
            QMenu::item {
                padding: 5px 20px;
            }
            QMenu::item:selected {
                background-color: #3498db;
            }
        """)

        # File menu
        file_menu = menubar.addMenu('File')

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        settings_action = QAction('Settings', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def show_settings(self):
        """Show settings dialog"""
        QMessageBox.information(self, "Settings", "Settings dialog will be implemented in a future version.")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About", "IPTV Tools v1.0.0\n\nA comprehensive tool for IPTV management and testing.")

def main():
    # Configure logging
    logging_config = {
        'version': 1,
        'formatters': {
            'standard': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': 'INFO',
                'formatter': 'standard',
                'stream': 'ext://sys.stdout'
            },
            'file': {
                'class': 'logging.FileHandler',
                'level': 'DEBUG',
                'formatter': 'standard',
                'filename': 'iptv_tools.log',
                'mode': 'a',
            }
        },
        'loggers': {
            'iptv_tools': {
                'handlers': ['console', 'file'],
                'level': 'DEBUG',
                'propagate': False
            }
        }
    }
    logging.config.dictConfig(logging_config)
    logger = logging.getLogger("iptv_tools")

    # Disable SSL warnings
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    logger.info("SSL warnings disabled")

    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
        logger.info("Created logs directory")

    # Create ERROR.txt file if it doesn't exist
    if not os.path.exists('ERROR.txt'):
        with open('ERROR.txt', 'w', encoding='utf-8') as f:
            f.write("# IPTV Tools Error Log\n")
            f.write("# Format: URL | Error Type\n")
            f.write(f"# Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        logger.info("Created ERROR.txt file")

    # Start application
    logger.info("Starting application")
    app = QApplication(sys.argv)

    # Create splash screen
    splash_pixmap = QPixmap(200, 200)
    splash_pixmap.fill(QColor("#3498db"))
    splash = QSplashScreen(splash_pixmap)
    splash.showMessage("Loading IPTV Tools...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
    splash.show()
    app.processEvents()

    # Create and show main window
    window = IPTVTools()

    # Close splash screen after a delay
    QTimer.singleShot(2000, splash.close)
    QTimer.singleShot(2000, window.show)

    # Exit when app is closed
    exit_code = app.exec_()
    logger.info(f"Application exiting with code {exit_code}")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
