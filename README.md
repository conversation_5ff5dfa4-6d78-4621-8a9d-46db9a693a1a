# IPTV Tools

A comprehensive suite of tools for managing and testing IPTV services.

## Features

- MAC Scanner - Test MAC addresses against portals
  - Scan multiple MAC addresses against IPTV portals
  - Generate random MAC addresses with custom prefixes
  - Support for proxy usage to avoid IP blocking
  - Telegram notifications for active MACs
- M3U Checker - Validate M3U playlists and extract channel information
- Xtream Code Checker - Test Xtream Code credentials
- XUI Panel Checker - Test XUI panel credentials
- Portal Extractor - Extract portal information
- Link Extractor - Extract links from websites

## Requirements

- Python 3.6 or higher
- PyQt5
- Requests
- BeautifulSoup4 (for some features)

## Installation

1. Make sure you have Python installed on your system.
2. Install the required dependencies:

```bash
pip install PyQt5 requests beautifulsoup4
```

## Running the Application

There are two ways to run the application:

### Simple Launcher

Run the simple launcher to start the application with a basic interface:

```bash
python run_app.py
```

### Full Application

Run the full application launcher to attempt to load all available tabs:

```bash
python run_full_app.py
```

## Troubleshooting

If you encounter any issues with the application, check the log file `iptv_tools.log` for more information.

Common issues:

- **Import errors**: Make sure all required dependencies are installed.
- **Missing modules**: Ensure all Python files are in the correct directory.
- **Proxy errors**: If using proxies, ensure they are in the correct format.

## License

This software is provided as-is without any warranty. Use at your own risk.

## Contact

For support, bug reports, or feature requests, please contact us through our official channels.
