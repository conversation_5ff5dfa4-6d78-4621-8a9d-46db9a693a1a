"""
Link Extractor Tab for IPTV Tools.
This module provides a UI tab for extracting links from text files.
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QFileDialog, QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QGroupBox, QCheckBox, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

from link_extractor import LinkExtractor

class LinkExtractorTab(QWidget):
    """Tab for Link Extractor functionality"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.extractor = LinkExtractor()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize UI components"""
        layout = QVBoxLayout()

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout()

        # Directory selection
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("Directory:"))
        self.dir_label = QLabel("No directory selected")
        self.dir_label.setStyleSheet("font-weight: bold;")
        dir_layout.addWidget(self.dir_label)

        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_directory)
        dir_layout.addWidget(self.browse_button)

        config_layout.addLayout(dir_layout)

        # Extraction method selection
        method_group = QGroupBox("Extraction Method")
        method_layout = QHBoxLayout()

        self.method1_check = QCheckBox("Method 1 (Standard)")
        self.method1_check.setChecked(True)
        self.method1_check.toggled.connect(self.toggle_extraction_method)
        method_layout.addWidget(self.method1_check)

        self.method2_check = QCheckBox("Method 2 (Advanced)")
        self.method2_check.setChecked(False)
        self.method2_check.toggled.connect(self.toggle_extraction_method)
        method_layout.addWidget(self.method2_check)

        method_group.setLayout(method_layout)
        config_layout.addWidget(method_group)

        # Save options
        options_layout = QHBoxLayout()

        self.save_xtream_check = QCheckBox("Save Xtream Code links")
        self.save_xtream_check.setChecked(True)
        options_layout.addWidget(self.save_xtream_check)

        self.save_xui_check = QCheckBox("Save XUI Panel links")
        self.save_xui_check.setChecked(True)
        options_layout.addWidget(self.save_xui_check)

        config_layout.addLayout(options_layout)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # Action buttons
        action_layout = QHBoxLayout()

        self.extract_button = QPushButton("Extract Links")
        self.extract_button.clicked.connect(self.extract_links)
        self.extract_button.setEnabled(False)
        action_layout.addWidget(self.extract_button)

        self.clear_button = QPushButton("Clear Results")
        self.clear_button.clicked.connect(self.clear_results)
        action_layout.addWidget(self.clear_button)

        layout.addLayout(action_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        # Results table
        self.results_table = QTableWidget(0, 4)
        self.results_table.setHorizontalHeaderLabels(["File", "Xtream Links", "XUI Links", "Total"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def connect_signals(self):
        """Connect signals from extractor to UI"""
        self.extractor.progress_signal.connect(self.update_progress)
        self.extractor.status_signal.connect(self.update_status)
        self.extractor.result_signal.connect(self.add_result)

    def browse_directory(self):
        """Browse for directory containing text files"""
        directory = QFileDialog.getExistingDirectory(self, "Select Directory")
        if directory:
            self.dir_label.setText(directory)
            self.extract_button.setEnabled(True)
            self.status_label.setText(f"Selected directory: {directory}")

    def toggle_extraction_method(self, checked):
        """Toggle between extraction methods"""
        # Ensure only one method is selected at a time
        if checked:
            sender = self.sender()
            if sender == self.method1_check and self.method2_check.isChecked():
                self.method2_check.setChecked(False)
            elif sender == self.method2_check and self.method1_check.isChecked():
                self.method1_check.setChecked(False)

            # If user unchecks both, re-check the one that was just unchecked
            if not self.method1_check.isChecked() and not self.method2_check.isChecked():
                if sender == self.method1_check:
                    self.method1_check.setChecked(True)
                else:
                    self.method2_check.setChecked(True)

    def extract_links(self):
        """Extract links from text files in selected directory"""
        directory = self.dir_label.text()
        if directory == "No directory selected":
            QMessageBox.warning(self, "Warning", "Please select a directory first.")
            return

        # Clear previous results
        self.clear_results()

        # Determine which extraction method to use
        use_method2 = self.method2_check.isChecked()

        # Extract links using the selected method
        if use_method2:
            # Use Method 2 (Advanced)
            self.status_label.setText("Using Advanced extraction method...")
            xtream_links, xui_links, total_files = self.extractor.extract_links_from_directory_method2(directory)
        else:
            # Use Method 1 (Standard)
            self.status_label.setText("Using Standard extraction method...")
            xtream_links, xui_links, total_files = self.extractor.extract_links_from_directory(directory)

        # Save links if requested
        if self.save_xtream_check.isChecked() and xtream_links:
            xtream_file = os.path.join(directory, "extracted_xtream_links.txt")
            with open(xtream_file, 'w', encoding='utf-8') as f:
                for link in xtream_links:
                    f.write(f"{link}\n")
            self.status_label.setText(f"Saved {len(xtream_links)} Xtream Code links to {xtream_file}")

        if self.save_xui_check.isChecked() and xui_links:
            xui_file = os.path.join(directory, "extracted_xui_links.txt")
            with open(xui_file, 'w', encoding='utf-8') as f:
                for link in xui_links:
                    f.write(f"{link}\n")
            self.status_label.setText(f"Saved {len(xui_links)} XUI Panel links to {xui_file}")

        # Show summary
        if xtream_links or xui_links:
            method_name = "Advanced" if use_method2 else "Standard"
            QMessageBox.information(
                self,
                "Extraction Complete",
                f"Using {method_name} method:\n"
                f"Found {len(xtream_links)} Xtream Code links and {len(xui_links)} XUI Panel links in {total_files} files.\n\n"
                f"Links have been saved to the selected directory."
            )

    def clear_results(self):
        """Clear results table"""
        self.results_table.setRowCount(0)
        self.progress_bar.setValue(0)
        self.status_label.setText("Ready")

    def add_result(self, result):
        """Add result to table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result.get('file_name', '')))
        self.results_table.setItem(row, 1, QTableWidgetItem(str(result.get('xtream_count', 0))))
        self.results_table.setItem(row, 2, QTableWidgetItem(str(result.get('xui_count', 0))))
        self.results_table.setItem(row, 3, QTableWidgetItem(str(result.get('total_links', 0))))

        # Highlight row if links found
        if result.get('total_links', 0) > 0:
            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                if item:
                    item.setBackground(QColor(200, 255, 200))  # Light green

    def update_progress(self, current, total):
        """Update progress bar"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def update_status(self, status):
        """Update status label"""
        self.status_label.setText(status)
